@tool
class_name ShadersS<PERSON>rite2d
extends Sprite2D
## Multi-pass shader processor for Sprite2D with real-time editor preview.
## [br][br]Usage:
## - Set base texture in 'shaders_texture'
## - Add shader materials to 'shaders_dic_copy' (key: shader name, value: material)
## - Click generate button to refresh effects
## [br][br]Warnings:
## - Do NOT manually set texture/material properties - they will be overwritten
## - Only supports CanvasItemMaterial/ShaderMaterial types
## - Editor preview may glitch with invalid configurations

## This property makes the subviewport background transparent. This property should not be modified at all.
const TRANSPARENT_BG: bool = true

## if texture show wrong,press it to refresh
@export_tool_button("generate") var genarete_func = generate

@export var shaders_texture: Texture2D:
	set(value):
		shaders_texture = value
		generate()
		update_configuration_warnings()
@export var shaders_dic: Dictionary:
	set(value):
		shaders_dic = value
		_update_dic_copy()
		# generate()
		update_configuration_warnings()

@export_group("Subviewport Setting")
## Expands the size to handle special shaders that expand the display range of the image
@export var size_expand: Vector2 = Vector2(1, 1)
@export var snap_2d_transforms_to_pixel: bool = false
@export var snap_2d_vertices_to_pixel: bool = false
@export var canvas_item_default_texture_filter: Viewport.DefaultCanvasItemTextureFilter = Viewport.DefaultCanvasItemTextureFilter.DEFAULT_CANVAS_ITEM_TEXTURE_FILTER_LINEAR
## Modifications are not recommended, as 3D rendering is generally not required
@export var disable_3d: bool = true

var first_sub_viewport: SubViewport # Root viewport for effect chain
var material_to_sprite_map: Dictionary[StringName, Sprite2D] = {}

var shaders_dic_copy: Dictionary[StringName, Material]
var _generate_locked := false

func _update_dic_copy():
	shaders_dic_copy.clear()
	for key in shaders_dic.keys():
		var mat = shaders_dic[key]
		if mat:
			# In the editor, use direct references for real-time uniform updates.
			# At runtime, use duplicates to prevent cross-talk between instances.
			if Engine.is_editor_hint():
				shaders_dic_copy[key] = mat
			else:
				shaders_dic_copy[key] = mat.duplicate()

func _ready() -> void:
	_update_dic_copy()
	generate()

## Clears all generated nodes and resets state
func clear():
	texture = null
	material = null
	if first_sub_viewport != null:
		first_sub_viewport.queue_free()
		first_sub_viewport = null
	material_to_sprite_map.clear()

## Updates shader parameter for specified material
func set_shader_param_by_name(choose_material_name: StringName, param: StringName, value: Variant):
	var choose_material = get_material_by_name(choose_material_name) as ShaderMaterial
	if choose_material == null:
		return null
	choose_material.set_shader_parameter(param, value)


func set_instance_shader_param_by_name(choose_material_name: StringName, param: StringName, value: Variant):
	if not material_to_sprite_map.has(choose_material_name):
		push_warning("Material '%s' not found in this ShadersSprite2D. Maybe generate() wasn't called or material name is wrong." % choose_material_name)
		return

	var sprite: Sprite2D = material_to_sprite_map[choose_material_name]
	sprite.set_instance_shader_parameter(param, value)


func add_material(material_name: StringName, new_material: Material) -> bool:
	if shaders_dic_copy.has(material_name):
		# push_warning("Try adding duplicate keys to the dictionary")
		return false
	shaders_dic_copy[material_name] = new_material
	generate()
	return true

func erase_material(material_name: StringName):
	if not shaders_dic_copy.has(material_name):
		# push_warning("Try removing attributes that aren't in the dictionary!")
		return
	shaders_dic_copy.erase(material_name)
	generate()

## Retrieves material from dictionary by name
func get_material_by_name(choose_material_name: StringName) -> Material:
	if not shaders_dic_copy.has(choose_material_name):
		push_warning(name + " shaders_dir" + "not has " + choose_material_name)
		return null
	return shaders_dic_copy[choose_material_name]

## Generates multi-pass shader effect chain
func generate():
	if _generate_locked:
		return

	_generate_locked = true

	if is_inside_tree():
		_deferred_generate.call_deferred()
	else:
		_generate_implementation()
		_generate_locked = false

func _deferred_generate():
	if shaders_texture is ViewportTexture:
		await RenderingServer.frame_post_draw

	if not self: # Node might have been freed.
		_generate_locked = false
		return

	_generate_implementation()
	_generate_locked = false

func _generate_implementation():
	clear()

	if shaders_texture == null:
		return

	var shaders_dic_size := shaders_dic_copy.size()

	## no shader and return
	if shaders_dic_size == 0:
		texture = shaders_texture
		return

	var last_sprite_2d: Sprite2D = self
	var shaders_keys: Array = shaders_dic_copy.keys()
	var shaders_array: Array = shaders_dic_copy.values()

	for shader in shaders_array:
		assert(shader.is_class("ShaderMaterial") or shader.is_class("CanvasItemMaterial"), "错误的sprite材质配置")

	for index in shaders_dic_size:
		var key = shaders_keys[index]
		last_sprite_2d.material = shaders_array[index]
		material_to_sprite_map[key] = last_sprite_2d

		if shaders_dic_size - index == 1:
			last_sprite_2d.texture = shaders_texture
		else:
			## add subviewport
			var new_subviewport: SubViewport = _get_subviewport()
			last_sprite_2d.add_child(new_subviewport)

			## node first subviewport
			if first_sub_viewport == null:
				first_sub_viewport = new_subviewport

			## add viewport-texture for last sprite2d
			last_sprite_2d.texture = new_subviewport.get_texture()

			## add new sprite2d
			var new_sprite_2d: Sprite2D = Sprite2D.new()
			new_sprite_2d.position = new_subviewport.size / 2.0
			new_subviewport.add_child(new_sprite_2d)
			last_sprite_2d = new_sprite_2d


## Creates configured SubViewport
func _get_subviewport() -> SubViewport:
	var subviewport = SubViewport.new()
	subviewport.disable_3d = disable_3d
	subviewport.transparent_bg = TRANSPARENT_BG
	subviewport.size = shaders_texture.get_size() * size_expand
	subviewport.snap_2d_transforms_to_pixel = snap_2d_transforms_to_pixel
	subviewport.snap_2d_vertices_to_pixel = snap_2d_vertices_to_pixel
	subviewport.canvas_item_default_texture_filter = canvas_item_default_texture_filter

	return subviewport


func _get_configuration_warnings():
	var warnings = []

	if shaders_dic_copy.size() <= 1:
		warnings.append("It is not recommended to use this node when there is no shader or only one shader.")

	var shader_materals := shaders_dic_copy.values()
	for shader_materal in shader_materals:
		if not (shader_materal.is_class("ShaderMaterial") or shader_materal.is_class("CanvasItemMaterial")):
			warnings.append("Only ShaderMaterial/CanvasItemMaterial supported.")
			break

	if shaders_texture == null:
		warnings.append("No bottom image provided; shaders won't take effect.")

	return warnings

## Delete the texture before saving, and assign the value again after saving
## to avoid the editor error by saving the automatically generated viewport texture to the scene file('Path to node is invalid')
## The solution comes from:
## https://forum.godotengine.org/t/how-to-make-variables-for-scripts-running-in-the-editor-not-save-to-the-scene-file-in-godot/104490/2
## Thanks for the guidance:https://forum.godotengine.org/u/mrcdk/summary
func _notification(what: int) -> void:
	match what:
		NOTIFICATION_EDITOR_PRE_SAVE:
			texture = null
		NOTIFICATION_EDITOR_POST_SAVE:
			if first_sub_viewport != null:
				texture = first_sub_viewport.get_texture()
