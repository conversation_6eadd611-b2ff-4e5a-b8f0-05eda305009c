[gd_scene load_steps=6 format=3 uid="uid://pkmdjmq1ouq3"]

[ext_resource type="PackedScene" uid="uid://drn0is2i67xvx" path="res://addons/simple-gui-transitions/example/layout_1.tscn" id="1"]
[ext_resource type="PackedScene" uid="uid://dlidtkkqbimv1" path="res://addons/simple-gui-transitions/example/layout_2.tscn" id="2"]
[ext_resource type="Script" uid="uid://clsegifd2kjxr" path="res://addons/simple-gui-transitions/example/main.gd" id="3"]
[ext_resource type="Theme" uid="uid://cxbdqv0cxcy1m" path="res://addons/simple-gui-transitions/example/theme.tres" id="4"]
[ext_resource type="PackedScene" uid="uid://cdba1niudigw2" path="res://addons/simple-gui-transitions/example/layout_3.tscn" id="5"]

[node name="Main" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("4")
script = ExtResource("3")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2

[node name="Control2" type="Control" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 0.1
mouse_filter = 2

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
mouse_filter = 2

[node name="Control2" type="Control" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 3.0
mouse_filter = 2

[node name="ButtonShow" type="Button" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
focus_mode = 0
text = "Show layout 1"

[node name="Control3" type="Control" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 0.05
mouse_filter = 2

[node name="Control" type="Control" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 8.0
mouse_filter = 2

[node name="Layout1" parent="." instance=ExtResource("1")]
layout_mode = 1

[node name="Layout2" parent="." instance=ExtResource("2")]
visible = false
layout_mode = 1

[node name="Layout3" parent="." instance=ExtResource("5")]
visible = false
layout_mode = 1

[connection signal="pressed" from="VBoxContainer/HBoxContainer/ButtonShow" to="." method="_on_ButtonShow_pressed"]
