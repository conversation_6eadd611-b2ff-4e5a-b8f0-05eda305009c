[gd_scene load_steps=5 format=3 uid="uid://drn0is2i67xvx"]

[ext_resource type="Script" uid="uid://bxj3lxsyhce2n" path="res://addons/simple-gui-transitions/example/layout_1.gd" id="1"]
[ext_resource type="Script" uid="uid://bimelmc1ce1b2" path="res://addons/simple-gui-transitions/transition.gd" id="2"]
[ext_resource type="Theme" uid="uid://cxbdqv0cxcy1m" path="res://addons/simple-gui-transitions/example/theme.tres" id="3"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="4_mqvbb"]

[node name="Layout1" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("3")
script = ExtResource("1")

[node name="GuiTransition" type="Node" parent="."]
script = ExtResource("2")
animation_enter = 0
animation_leave = 0
duration = 2.0
delay = 1.0
layout = NodePath("..")
controls = Array[NodePath]([NodePath("../HBoxContainer/VBoxContainer/ButtonUpdate"), NodePath("../HBoxContainer/VBoxContainer/ButtonHide")])

[node name="HBoxContainer" type="HBoxContainer" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Control2" type="Control" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="VBoxContainer" type="VBoxContainer" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Control3" type="Control" parent="HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Label" type="Label" parent="HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
text = "Layout 1"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonUpdate" type="Button" parent="HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
focus_mode = 0
text = "Update layout 1"

[node name="Sprite2D" type="Sprite2D" parent="HBoxContainer/VBoxContainer/ButtonUpdate"]
texture = ExtResource("4_mqvbb")

[node name="ButtonHide" type="Button" parent="HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
focus_mode = 0
text = "Hide layout 1"

[node name="ButtonGoTo" type="Button" parent="HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
focus_mode = 0
text = "Go to layout 2"

[node name="Control4" type="Control" parent="HBoxContainer/VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Control" type="Control" parent="HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[connection signal="pressed" from="HBoxContainer/VBoxContainer/ButtonUpdate" to="." method="_on_ButtonUpdate_pressed"]
[connection signal="pressed" from="HBoxContainer/VBoxContainer/ButtonHide" to="." method="_on_ButtonHide_pressed"]
[connection signal="pressed" from="HBoxContainer/VBoxContainer/ButtonGoTo" to="." method="_on_ButtonGoTo_pressed"]
