@icon("res://addons/simple-gui-transitions/icon.png")
extends Node
## GUI过渡动画节点
##
## 此节点负责管理特定布局的过渡动画效果。
## 默认的过渡设置可以在项目设置中配置：
## [code]Project Settings > GUI Transitions > Config[/code]
## 这些设置将应用到 [code]GuiTransition[/code] 节点的默认属性上。
## 这对于在整个项目中统一调整动画速度等参数非常有用。


# 枚举定义
## 进入和离开过渡动画的可用动画类型
enum Anim {
	DEFAULT = -1,  ## 使用项目设置中的默认动画
	SLIDE_LEFT = 0,  ## 向屏幕左侧滑动
	SLIDE_RIGHT = 1,  ## 向屏幕右侧滑动
	SLIDE_UP = 2,  ## 向屏幕上方滑动
	SLIDE_DOWN = 3,  ## 向屏幕下方滑动
	FADE = 4,  ## 原地淡入淡出
	SCALE = 5,  ## 基于中心点缩放
	SCALE_VERTICAL = 6,  ## 基于中心点水平缩放
	SCALE_HORIZONTAL = 7,  ## 基于中心点垂直缩放
}

## 当前过渡状态
enum Status {
	OK = 0,  ## 没有正在执行的过渡动画
	SHOWING = 1,  ## 正在执行进入过渡动画
	HIDING = 2,  ## 正在执行离开过渡动画
}

## 过渡属性使用的布尔值枚举
enum ExportBool {
	DEFAULT = -1,  ## 使用项目设置中的默认值
	TRUE = 1,  ## 启用属性
	FALSE = 0,  ## 禁用属性
}


# 预加载资源
const MaterialTransform := preload("res://addons/simple-gui-transitions/materials/transform.tres")
const DefaultValues := preload("res://addons/simple-gui-transitions/default_values.gd")

# 内部类定义
## 节点信息类
## 存储单个节点的动画相关信息和状态
class NodeInfo extends RefCounted:
	# 基础节点信息
	var node: Control  ## 目标控制节点
	var name: String  ## 节点名称
	var initial_position: Vector2  ## 初始位置
	var initial_scale: Vector2  ## 初始缩放
	var initial_mouse_filter: int  ## 初始鼠标过滤模式
	var initial_anchor_h: Vector2  ## 初始水平锚点
	var initial_anchor_v: Vector2  ## 初始垂直锚点

	# 动画参数
	var delay: float  ## 动画延迟时间
	var duration: float  ## 动画持续时间
	var center_pivot: bool  ## 是否使用中心轴点
	var tween: Tween  ## 动画补间对象
	var layout_only: bool  ## 是否仅布局模式
	var is_layout_managed: bool  ## 是否由布局系统管理
	var animation_enter: int  ## 进入动画类型
	var animation_leave: int  ## 离开动画类型

	# SubViewport包裹相关属性（用于滑动动画的shader效果）
	var viewport_container: SubViewportContainer  ## 视口包裹容器
	var viewport: SubViewport  ## 子视口
	var original_parent: Node  ## 原始父节点
	var original_index: int  ## 在父节点中的原始索引
	var needs_viewport_wrapper: bool  ## 是否需要视口包裹
	var wrapper_created: bool = false  ## 包裹结构是否已创建
	var uses_direct_shader: bool = false  ## 是否使用直接shader（性能优化）
	var original_theme: Theme  ## 从场景树继承的主题资源
	var original_node_theme: Theme  ## 节点原始主题状态
	var content_bounds: Rect2  ## 节点内容边界

	# 原始布局属性备份
	var original_layout_mode: int  ## 原始布局模式
	var original_anchors_preset: int  ## 原始锚点预设
	var original_size: Vector2  ## 原始尺寸
	var original_node_position: Vector2  ## 原始节点位置（用于SubViewport包裹恢复）

	## 初始化节点信息
	## 设置动画参数并保存节点的初始状态
	func _init(
		_node: Control,
		_delay: float,
		_duration: float,
		_animation_enter: int,
		_animation_leave: int,
		_auto_start: bool,
		_center_pivot: bool,
		_layout_only: bool
	) -> void:
		# 基础属性设置
		node = _node
		name = node.name
		initial_scale = Vector2(node.scale)
		initial_mouse_filter = node.mouse_filter
		delay = _delay
		duration = _duration
		center_pivot = _center_pivot
		layout_only = _layout_only
		animation_enter = _animation_enter
		animation_leave = _animation_leave

		# 检测节点是否由布局系统管理
		is_layout_managed = _is_layout_managed_node(node)

		# 保存节点初始状态
		initial_position = Vector2(node.position)
		initial_anchor_h = Vector2(node.anchor_left, node.anchor_right)
		initial_anchor_v = Vector2(node.anchor_top, node.anchor_bottom)

		# 智能判断是否需要SubViewport包裹（性能优化）
		needs_viewport_wrapper = _should_use_viewport_wrapper()

		# 如果是shader动画但不需要SubViewport包裹，则使用直接shader
		var is_shader_animation = _is_shader_animation()
		uses_direct_shader = is_shader_animation and not needs_viewport_wrapper

		# 延迟创建SubViewport包裹结构或初始化直接shader

		if _auto_start:
			_set_node_and_children_alpha(node, 0.0)

		set_pivot_to_center()

	## 智能判断是否需要SubViewport包裹（性能优化）
	## 采用激进的优化策略：优先使用直接shader，只在真正必要时才使用SubViewport包裹
	func _should_use_viewport_wrapper() -> bool:
		# 如果是仅布局模式，不需要包裹
		if layout_only:
			return false

		# 检查是否为shader动画（滑动和缩放）
		var shader_animations := [
			Anim.SLIDE_LEFT,
			Anim.SLIDE_RIGHT,
			Anim.SLIDE_UP,
			Anim.SLIDE_DOWN,
			Anim.SCALE,
			Anim.SCALE_HORIZONTAL,
			Anim.SCALE_VERTICAL
		]

		var is_shader_animation = (animation_enter in shader_animations or animation_leave in shader_animations)
		if not is_shader_animation:
			return false

		# 激进优化策略：只有在以下情况才使用SubViewport包裹
		# 1. 节点已有自定义shader材质（避免冲突）
		if node.material and node.material is ShaderMaterial:
			return true

		# 2. 节点有子节点（Control或Node2D类型）
		if _has_relevant_children():
			return true

		# 对于其他所有情况（包括在布局容器中的简单节点），都使用直接shader
		return false

	## 检查节点是否有相关的子节点
	func _has_relevant_children() -> bool:
		for child in node.get_children():
			if child is Control or child is Node2D:
				return true
		return false



	## 初始化直接shader（性能优化路径）
	func _ensure_direct_shader_initialized() -> void:
		if not uses_direct_shader:
			return

		# 为节点直接应用shader材质
		if not node.material or not (node.material is ShaderMaterial):
			var shader_material = ShaderMaterial.new()
			shader_material.shader = MaterialTransform.shader
			node.material = shader_material

		# 初始化shader参数
		var shader_mat = node.material as ShaderMaterial
		if shader_mat:
			shader_mat.set_shader_parameter("slide", Vector2.ZERO)
			shader_mat.set_shader_parameter("scale", Vector2.ONE)
			shader_mat.set_shader_parameter("node_size", node.size)

	## 检测节点是否在布局容器中
	func _is_in_layout_container() -> bool:
		var parent = node.get_parent()
		if not parent:
			return false

		# 检查常见的布局容器类型
		return (parent is VBoxContainer or
				parent is HBoxContainer or
				parent is GridContainer or
				parent is FlowContainer or
				parent is MarginContainer or
				parent is CenterContainer or
				parent is AspectRatioContainer or
				parent is SplitContainer)

	# 等待布局系统完成尺寸计算
	func _wait_for_layout_completion() -> void:
		# 等待至少一帧让布局系统完成基本计算
		await node.get_tree().process_frame

		# 如果节点在布局容器中，需要额外等待
		if _is_in_layout_container():
			# 布局容器可能需要多帧来完成尺寸计算
			await node.get_tree().process_frame
			await node.get_tree().process_frame

		# 额外的安全等待，确保所有布局更新完成
		await node.get_tree().process_frame

	# 确保SubViewport包裹结构已创建（延迟创建）
	func _ensure_viewport_wrapper_created() -> void:
		if not needs_viewport_wrapper or wrapper_created:
			return

		# 等待布局完成后再获取尺寸和创建包裹
		await _wait_for_layout_completion()

		# 在布局完成后创建包裹结构
		_create_viewport_wrapper()
		wrapper_created = true

	## 计算节点内容边界
	## 返回节点本身的边界矩形，不考虑扩展子节点
	func _calculate_content_bounds() -> Rect2:
		return Rect2(Vector2.ZERO, node.size)

	## 计算SubViewport的最优尺寸
	## 根据动画类型决定是否添加智能边距
	func _calculate_optimal_viewport_size() -> Vector2:
		var required_width = node.size.x
		var required_height = node.size.y

		# 检查是否为shader动画
		var is_shader_animation = _is_shader_animation()

		# 计算智能边距
		var smart_margin = Vector2.ZERO
		if is_shader_animation:
			# 滑动动画使用精确尺寸，避免视觉异常
			# 这是修复混合滑动配置（如SLIDE_DOWN + SLIDE_LEFT）宽度异常的关键
			smart_margin = Vector2.ZERO
		else:
			# 非滑动动画添加适当边距以提供缓冲空间
			smart_margin.x = max(4.0, required_width * 0.02)
			smart_margin.y = max(4.0, required_height * 0.02)

		return Vector2(required_width + smart_margin.x, required_height + smart_margin.y)

	## 计算节点在SubViewport中的偏移位置
	## 确保节点在视口中正确居中显示
	func _calculate_node_offset_in_viewport() -> Vector2:
		var offset = Vector2.ZERO

		# shader动画使用精确尺寸，不需要居中偏移
		if not _is_shader_animation():
			# 非滑动动画需要居中偏移以适应智能边距
			var viewport_size = _calculate_optimal_viewport_size()
			var content_size = node.size

			# 如果视口比内容大，添加居中偏移
			if viewport_size.x > content_size.x:
				offset.x = (viewport_size.x - content_size.x) * 0.5
			if viewport_size.y > content_size.y:
				offset.y = (viewport_size.y - content_size.y) * 0.5

		return offset

	## 计算节点在原始容器中的绝对位置
	## 根据锚点设置和容器尺寸计算节点的实际显示位置
	func _calculate_absolute_position_in_container() -> Vector2:
		if not original_parent:
			print("警告：无法计算绝对位置，原始父节点为空")
			return Vector2.ZERO

		# 获取原始容器尺寸
		var container_size = original_parent.size
		print("容器尺寸：", container_size)
		print("节点原始锚点：left=", initial_anchor_h.x, ", right=", initial_anchor_h.y, ", top=", initial_anchor_v.x, ", bottom=", initial_anchor_v.y)
		print("节点原始位置：", original_node_position)
		print("节点原始尺寸：", original_size)

		# 计算锚点在容器中的绝对位置
		var anchor_left_pos = initial_anchor_h.x * container_size.x
		var anchor_right_pos = initial_anchor_h.y * container_size.x
		var anchor_top_pos = initial_anchor_v.x * container_size.y
		var anchor_bottom_pos = initial_anchor_v.y * container_size.y

		# 计算锚点区域的中心位置
		var anchor_center_x = (anchor_left_pos + anchor_right_pos) * 0.5
		var anchor_center_y = (anchor_top_pos + anchor_bottom_pos) * 0.5

		# 节点的绝对位置 = 锚点中心位置 + 原始偏移
		var absolute_position = Vector2(anchor_center_x, anchor_center_y) + original_node_position

		print("锚点中心位置：(", anchor_center_x, ", ", anchor_center_y, ")")
		print("计算出的绝对位置：", absolute_position)

		return absolute_position

	## 检查是否为shader动画
	## 包括滑动动画和缩放动画
	func _is_shader_animation() -> bool:
		var shader_animations = [
			Anim.SLIDE_LEFT, Anim.SLIDE_RIGHT, Anim.SLIDE_UP, Anim.SLIDE_DOWN,
			Anim.SCALE, Anim.SCALE_HORIZONTAL, Anim.SCALE_VERTICAL
		]
		return animation_enter in shader_animations or animation_leave in shader_animations

	## 获取场景树中的主题资源
	func _find_inherited_theme() -> Theme:
		var current_node = original_parent
		while current_node:
			if current_node.has_method("get_theme") and current_node.get_theme():
				return current_node.get_theme()
			current_node = current_node.get_parent()

		# 如果没有找到，尝试获取全局主题
		return ThemeDB.get_default_theme()

	# 创建SubViewport包裹结构
	func _create_viewport_wrapper() -> void:
		print("=== 🏗️ 开始创建SubViewport包裹结构 ===")
		print("  帧号:", Engine.get_process_frames())

		# 🔍 记录包裹创建前的节点状态
		print("--- 📊 包裹创建前节点状态 ---")
		print("  visible:", node.visible)
		print("  modulate.a:", node.modulate.a)
		print("  position:", node.position)
		print("  global_position:", node.global_position)
		print("  size:", node.size)
		print("--------------------------------")

		# 保存原始父节点信息
		original_parent = node.get_parent()
		if not original_parent:
			push_warning("无法创建SubViewport包裹：节点没有父节点")
			needs_viewport_wrapper = false
			return

		original_index = node.get_index()
		print("原始父节点：", original_parent.name, "，索引：", original_index)

		# 保存原始主题信息
		original_theme = _find_inherited_theme()
		original_node_theme = node.get_theme()  # 保存节点原始主题状态（可能为null）

		# 关键修复：在布局完成后获取真实的节点尺寸
		# 此时布局系统已经完成了尺寸计算，获取到的是正确的显示尺寸
		original_size = Vector2(node.size)
		print("节点原始尺寸：", original_size)

		# 保存节点的原始布局属性
		original_layout_mode = node.get("layout_mode") if node.has_method("get") and node.get("layout_mode") != null else 0
		original_anchors_preset = node.get("anchors_preset") if node.has_method("get") and node.get("anchors_preset") != null else 0
		print("原始布局模式：", original_layout_mode, "，锚点预设：", original_anchors_preset)



		# 关键修复1：直接在目标节点上设置主题，确保子节点能够正确继承
		if original_theme:
			node.set_theme(original_theme)

		# 计算内容边界，为子节点内容预留空间
		content_bounds = _calculate_content_bounds()

		# 创建SubViewportContainer
		viewport_container = SubViewportContainer.new()
		viewport_container.name = node.name + "_ViewportContainer"
		viewport_container.stretch = true

		# 创建SubViewport
		viewport = SubViewport.new()
		viewport.name = node.name + "_Viewport"
		viewport.render_target_update_mode = SubViewport.UPDATE_ALWAYS
		viewport.handle_input_locally = false  # 让输入事件穿透

		# 保存原始位置用于恢复
		original_node_position = node.position
		print("保存的原始位置：", original_node_position)

		# 核心解决方案：计算节点的绝对位置并重置锚点
		var absolute_position = _calculate_absolute_position_in_container()
		print("--- 锚点重置与绝对位置计算 ---")
		print("重置前 - 锚点：(", node.anchor_left, ", ", node.anchor_top, ", ", node.anchor_right, ", ", node.anchor_bottom, ")")
		print("重置前 - 位置：", node.position)

		# 重置节点锚点为左上角（0,0,0,0）
		node.anchor_left = 0.0
		node.anchor_right = 0.0
		node.anchor_top = 0.0
		node.anchor_bottom = 0.0

		# 设置计算出的绝对位置
		node.position = absolute_position
		print("重置后 - 锚点：(", node.anchor_left, ", ", node.anchor_top, ", ", node.anchor_right, ", ", node.anchor_bottom, ")")
		print("重置后 - 位置：", node.position)
		print("----------------------------------------")

		# 关键修复：SubViewportContainer应该使用原始位置，而不是重置后的绝对位置
		# 因为SubViewportContainer需要在原始父容器中占据正确的位置
		print("=== SubViewportContainer位置设置 ===")
		print("节点重置后的绝对位置：", node.position)
		print("节点原始位置：", original_node_position)
		print("节点原始尺寸：", original_size)
		print("容器父节点：", original_parent.name)
		print("容器父节点尺寸：", original_parent.size)

		# SubViewportContainer应该使用节点的原始位置和尺寸
		# 这样它在原始父容器中就能占据正确的位置
		viewport_container.size = original_size
		viewport_container.position = original_node_position

		print("设置SubViewportContainer尺寸：", viewport_container.size)
		print("设置SubViewportContainer位置：", viewport_container.position)
		print("SubViewportContainer全局位置：", viewport_container.global_position)
		print("=======================================")

		# 关键修复：复制原节点的size flags到容器
		viewport_container.size_flags_horizontal = node.size_flags_horizontal
		viewport_container.size_flags_vertical = node.size_flags_vertical
		viewport_container.size_flags_stretch_ratio = node.size_flags_stretch_ratio

		# 设置容器的最小尺寸，确保布局容器能正确分配空间
		viewport_container.custom_minimum_size = node.size

		# 关键修复2：根据精确的内容边界动态调整SubViewport尺寸
		var viewport_size = _calculate_optimal_viewport_size()

		# 当stretch=false时，手动设置SubViewport的尺寸
		viewport_container.stretch = false  # 禁用stretch以精确控制尺寸
		viewport.size = Vector2i(int(viewport_size.x), int(viewport_size.y))

		# 复制节点的布局属性到容器
		if node.get("layout_mode"):
			viewport_container.layout_mode = node.layout_mode
		if node.get("anchors_preset"):
			viewport_container.anchors_preset = node.anchors_preset
		viewport_container.anchor_left = node.anchor_left
		viewport_container.anchor_right = node.anchor_right
		viewport_container.anchor_top = node.anchor_top
		viewport_container.anchor_bottom = node.anchor_bottom

		# 将shader材质应用到容器而不是目标节点
		viewport_container.material = MaterialTransform.duplicate()

		# 初始化shader参数，包括节点尺寸
		var shader_mat = viewport_container.material as ShaderMaterial
		if shader_mat:
			shader_mat.set_shader_parameter("slide", Vector2.ZERO)
			shader_mat.set_shader_parameter("scale", Vector2.ONE)
			shader_mat.set_shader_parameter("node_size", viewport_size)

		# 构建包裹结构
		viewport_container.add_child(viewport)

		# 🔄 关键步骤：节点重新父化
		print("🔄 开始节点重新父化过程...")
		print("  重新父化前 - visible:", node.visible, ", modulate.a:", node.modulate.a)
		print("  重新父化前 - position:", node.position)
		print("  帧号:", Engine.get_process_frames())

		# 将目标节点移动到SubViewport中
		if original_parent:
			print("  🏠 添加SubViewportContainer到原始父节点...")
			original_parent.add_child(viewport_container)
			original_parent.move_child(viewport_container, original_index)
			print("  SubViewportContainer已添加到位置:", original_index)

			# 安全地重新父化节点
			print("  📤 从原始父节点移除节点...")
			node.get_parent().remove_child(node)
			print("  移除后节点状态 - visible:", node.visible, ", modulate.a:", node.modulate.a)

			print("  📥 将节点添加到SubViewport...")
			viewport.add_child(node)
			print("  添加后节点状态 - visible:", node.visible, ", modulate.a:", node.modulate.a)
			print("  节点新父节点:", node.get_parent().name)

		# 关键修复：在SubViewport中，节点应该位于(0,0)位置
		# 因为SubViewportContainer已经处理了在父容器中的定位
		print("=== SubViewport内部节点设置 ===")
		print("重置前 - SubViewport内节点位置：", node.position)
		print("重置前 - SubViewport内节点尺寸：", node.size)

		# 在SubViewport内部，节点应该从(0,0)开始
		node.position = Vector2.ZERO
		node.size = original_size

		print("重置后 - SubViewport内节点位置：", node.position)
		print("重置后 - SubViewport内节点尺寸：", node.size)
		print("SubViewport尺寸：", viewport.size)
		print("=================================")



		# 重置布局模式，避免自动布局影响尺寸
		if node.has_method("set"):
			node.set("layout_mode", 0)  # 设置为无布局模式

		# 注意：我们已经重置了锚点设置，这是解决坐标冲突的关键
		# 在cleanup_viewport_wrapper中会恢复原始锚点设置

		# 保持节点的原始尺寸，不要强制填满整个视口
		# 这样可以避免破坏子节点的相对位置关系

		print("SubViewport包裹结构创建完成")
		print("===============================")

	# Invalidates existing tween and creates a new one.
	func init_tween() -> void:
		if tween and tween.is_valid():
			tween.kill()

		tween = node.create_tween()

	# Set node to unclickable while in transition.
	func unset_clickable():
		node.mouse_filter = Control.MOUSE_FILTER_IGNORE

	# Revert initial node clickable value after transition.
	func revert_clickable():
		node.mouse_filter = initial_mouse_filter

	# Get the zero scale of node according to the animation type.
	func get_target_scale(animation: int) -> Vector2:
		var target_scale := Vector2.ZERO

		if animation == Anim.SCALE_HORIZONTAL:
			target_scale.y = initial_scale.y

		elif animation == Anim.SCALE_VERTICAL:
			target_scale.x = initial_scale.x

		return target_scale

	# Get the out-of-screen position of node according to the animation type.
	func get_target_position(animation: int) -> Vector2:
		# 修复：始终使用主视口尺寸，确保所有渲染路径的滑动距离一致
		var view_size := node.get_tree().get_root().get_visible_rect().size
		var offset := Vector2.ZERO

		print("=== 滑动偏移量计算分析 ===")
		print("屏幕分辨率：", view_size)
		print("节点尺寸：", node.size)
		print("节点位置：", node.position)
		print("动画类型：", animation)

		# 可配置的偏移倍数 - 当前使用2.0倍屏幕尺寸
		# 设计原因分析：
		# 1. 确保完全隐藏：即使节点很大，2倍屏幕宽度也能确保完全移出视野
		# 2. 避免视觉闪烁：足够的距离确保动画开始时节点完全不可见
		# 3. 兼容性考虑：适用于各种屏幕分辨率和节点尺寸
		var offset_multiplier = 1.0

		match animation:
			Anim.SLIDE_LEFT:
				offset.x = -view_size.x * offset_multiplier
				print("SLIDE_LEFT偏移：", offset.x, " (屏幕宽度 * ", offset_multiplier, ")")
			Anim.SLIDE_RIGHT:
				offset.x = view_size.x * offset_multiplier
				print("SLIDE_RIGHT偏移：", offset.x, " (屏幕宽度 * ", offset_multiplier, ")")
			Anim.SLIDE_UP:
				offset.y = -view_size.y * offset_multiplier
				print("SLIDE_UP偏移：", offset.y, " (屏幕高度 * ", offset_multiplier, ")")
			Anim.SLIDE_DOWN:
				offset.y = view_size.y * offset_multiplier
				print("SLIDE_DOWN偏移：", offset.y, " (屏幕高度 * ", offset_multiplier, ")")

		# 分析不同倍数的效果
		var node_width = node.size.x
		var node_height = node.size.y
		print("--- 偏移量分析 ---")
		print("1倍屏幕宽度偏移：", -view_size.x, " (理论最小值)")
		print("1.5倍屏幕宽度偏移：", -view_size.x * 1.5, " (安全边距)")
		print("2倍屏幕宽度偏移：", -view_size.x * 2.0, " (当前使用)")
		print("节点宽度：", node_width)
		print("节点完全隐藏所需偏移：", -(view_size.x + node_width))
		print("==================")

		return offset

	# Reset node scale to initial values.
	func reset_scale() -> void:
		node.scale = initial_scale

	# Reset node anchors to initial values.
	func reset_anchors(direction: String) -> void:
		if direction in ["both", "h"]:
			node.anchor_left = initial_anchor_h.x
			node.anchor_right = initial_anchor_h.y
		if direction in ["both", "v"]:
			node.anchor_top = initial_anchor_v.x
			node.anchor_bottom = initial_anchor_v.y

	# Get the out-of-screen anchor factor of node according to the animation type.
	func get_target_anchor(animation: int) -> Vector2:
		var offset := Vector2.ZERO

		match animation:
			Anim.SLIDE_LEFT:
				offset = Vector2(initial_anchor_h.x - 1.0, initial_anchor_h.y - 1.0)
			Anim.SLIDE_RIGHT:
				offset = Vector2(initial_anchor_h.x + 1.0, initial_anchor_h.y + 1.0)
			Anim.SLIDE_UP:
				offset = Vector2(initial_anchor_v.x - 1.0, initial_anchor_v.y - 1.0)
			Anim.SLIDE_DOWN:
				offset = Vector2(initial_anchor_v.x + 1.0, initial_anchor_v.y + 1.0)

		return offset

	func set_pivot_to_center() -> void:
		if center_pivot:
			node.pivot_offset = node.size / 2

	# 设置视觉偏移：支持SubViewport包裹、直接shader或传统方式
	func set_visual_offset(offset: Vector2) -> void:
		print("设置视觉偏移 - 偏移值：", offset)
		print("  节点当前位置：", node.position)
		print("  节点当前锚点：(", node.anchor_left, ", ", node.anchor_top, ", ", node.anchor_right, ", ", node.anchor_bottom, ")")

		if needs_viewport_wrapper and viewport_container:
			print("  使用SubViewport包裹模式")
			print("  SubViewportContainer当前位置：", viewport_container.position)
			print("  SubViewportContainer当前尺寸：", viewport_container.size)
			print("  SubViewportContainer全局位置：", viewport_container.global_position)
			# 使用SubViewport包裹时，操作容器的shader
			var _shader := viewport_container.material as ShaderMaterial
			if _shader:
				# 关键修复：当偏移非常接近零时，直接设置为零，避免shader精度问题
				var final_offset = offset
				if offset.length() < 0.01:  # 小于0.01像素时视为零
					final_offset = Vector2.ZERO
				_shader.set_shader_parameter("slide", final_offset)
				print("  已设置SubViewport容器shader参数：slide=", final_offset)
		elif uses_direct_shader:
			print("  使用直接shader模式")
			# 性能优化：直接在节点上应用shader
			var _shader := node.material as ShaderMaterial
			if _shader:
				var final_offset = offset
				if offset.length() < 0.01:  # 小于0.01像素时视为零
					final_offset = Vector2.ZERO
				_shader.set_shader_parameter("slide", final_offset)
				print("  已设置节点shader参数：slide=", final_offset)
		elif is_layout_managed:
			print("  使用布局管理模式")
			# 对于布局管理的节点，使用shader参数进行视觉位移（传统方式）
			var _shader := node.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("slide", offset)
				print("  已设置布局管理shader参数：slide=", offset)
		else:
			print("  使用传统position模式")
			# 对于非布局管理的节点，继续使用position（保持兼容性）
			var new_position = initial_position + offset
			node.position = new_position
			print("  已设置节点position：", new_position, " (初始位置：", initial_position, " + 偏移：", offset, ")")

	## 设置视觉缩放：支








	## 设置视觉缩放：支持SubViewport包裹、直接shader或传统方式
	func set_visual_scale(scale: Vector2) -> void:
		if needs_viewport_wrapper and viewport_container:
			# 核心修复：不再需要每帧强制重置position
			# 通过锚点重置和绝对位置计算已经解决了坐标系统冲突问题
			print("设置视觉缩放 - SubViewport模式")
			print("  当前节点位置：", node.position)
			print("  当前节点锚点：(", node.anchor_left, ", ", node.anchor_top, ", ", node.anchor_right, ", ", node.anchor_bottom, ")")
			print("  SubViewportContainer位置：", viewport_container.position)
			print("  SubViewportContainer尺寸：", viewport_container.size)
			print("  SubViewportContainer全局位置：", viewport_container.global_position)
			print("  缩放值：", scale)

			# 使用SubViewport包裹时，操作容器的shader
			var _shader := viewport_container.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("scale", scale)
				_shader.set_shader_parameter("node_size", viewport.size)
				print("  已设置shader参数：scale=", scale, ", node_size=", viewport.size)
		elif uses_direct_shader:
			# 性能优化：直接在节点上应用shader
			print("设置视觉缩放 - 直接shader模式")
			print("  缩放值：", scale)
			var _shader := node.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("scale", scale)
				_shader.set_shader_parameter("node_size", node.size)
				print("  已设置shader参数：scale=", scale, ", node_size=", node.size)
		elif is_layout_managed:
			# 对于布局管理的节点，使用shader参数进行视觉缩放（传统方式）
			print("设置视觉缩放 - 布局管理模式")
			print("  缩放值：", scale)
			var _shader := node.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("scale", scale)
				print("  已设置shader参数：scale=", scale)
		else:
			# 对于非布局管理的节点，直接设置scale属性（保持兼容性）
			print("设置视觉缩放 - 传统模式")
			print("  缩放值：", scale)
			node.scale = scale
			print("  已设置节点scale属性：", node.scale)

	# 清理shader参数
	func reset_shader() -> void:
		if needs_viewport_wrapper and viewport_container:
			# 使用SubViewport包裹时，清理容器的shader
			var _shader := viewport_container.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("slide", Vector2.ZERO)
				_shader.set_shader_parameter("scale", Vector2.ONE)
		elif uses_direct_shader:
			# 性能优化：清理直接应用的shader
			var _shader := node.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("slide", Vector2.ZERO)
				_shader.set_shader_parameter("scale", Vector2.ONE)
		elif is_layout_managed:
			# 传统方式：清理节点的shader
			var _shader := node.material as ShaderMaterial
			if _shader:
				_shader.set_shader_parameter("slide", Vector2.ZERO)
				_shader.set_shader_parameter("scale", Vector2.ONE)

	## 清理直接shader（性能优化路径）
	func cleanup_direct_shader() -> void:
		if not uses_direct_shader:
			return

		# 清理shader参数
		reset_shader()

		# 移除shader材质（如果是我们添加的）
		if node.material and node.material is ShaderMaterial:
			var shader_mat = node.material as ShaderMaterial
			if shader_mat.shader == MaterialTransform.shader:
				node.material = null

	# 清理SubViewport包裹结构
	func cleanup_viewport_wrapper() -> void:
		if not needs_viewport_wrapper or not viewport_container:
			return

		# 关键修复：在重新父化之前先清理shader，避免视觉间隙
		reset_shader()

		# 将节点恢复到原始父节点中
		if original_parent and is_instance_valid(original_parent):
			# 关键修复：恢复节点的原始主题状态
			node.set_theme(original_node_theme)  # 恢复原始主题（可能为null）

			# 恢复节点的原始属性
			node.position = original_node_position  # 使用保存的原始位置
			node.size = original_size  # 恢复原始尺寸
			node.anchor_left = initial_anchor_h.x
			node.anchor_right = initial_anchor_h.y
			node.anchor_top = initial_anchor_v.x
			node.anchor_bottom = initial_anchor_v.y

			# 恢复原始布局属性
			if node.has_method("set"):
				node.set("layout_mode", original_layout_mode)
				node.set("anchors_preset", original_anchors_preset)

			# 将节点重新父化到原始位置
			if node.get_parent():
				node.get_parent().remove_child(node)
			original_parent.add_child(node)
			original_parent.move_child(node, original_index)

		# 清理包裹容器
		if is_instance_valid(viewport_container):
			viewport_container.queue_free()

		# 重置引用
		viewport_container = null
		viewport = null
		original_parent = null
		original_theme = null
		original_node_theme = null
		content_bounds = Rect2()
		original_layout_mode = 0
		original_anchors_preset = 0
		original_size = Vector2.ZERO
		wrapper_created = false

	# 递归设置节点及其所有Control类型子节点的透明度
	func _set_node_and_children_alpha(target_node: Control, alpha: float) -> void:
		print("🎨 设置透明度 - 节点:", target_node.name, ", alpha:", alpha)
		print("  设置前 modulate.a:", target_node.modulate.a)
		print("  设置前 visible:", target_node.visible)
		print("  帧号:", Engine.get_process_frames())

		target_node.modulate.a = alpha

		print("  设置后 modulate.a:", target_node.modulate.a)
		print("  设置后 visible:", target_node.visible)

		# 递归处理子节点
		var child_count = 0
		for child in target_node.get_children():
			if child is Control:
				child_count += 1
				_set_node_and_children_alpha(child, alpha)

		if child_count > 0:
			print("  已处理", child_count, "个Control子节点")

	# 递归设置节点及其所有Control类型子节点的缩放
	func _set_node_and_children_scale(target_node: Control, scale_value: Vector2) -> void:
		target_node.scale = scale_value
		for child in target_node.get_children():
			if child is Control:
				_set_node_and_children_scale(child, scale_value)

	# 检测节点是否由布局系统管理
	func _is_layout_managed_node(target_node: Control) -> bool:
		# 检查节点是否使用布局模式（layout_mode > 0）
		if target_node.get("layout_mode") and target_node.layout_mode > 0:
			return true

		# 检查父节点是否是布局容器
		var parent = target_node.get_parent()
		if parent and parent.is_class("Container"):
			return true

		return false




# 常量定义


# 变量定义
# 公共导出变量
@export_group("过渡动画")

## 当前布局是否在启动时自动触发过渡动画
## 默认启用
@export var auto_start: ExportBool = ExportBool.DEFAULT

## 是否在单个控件动画的同时对整个布局进行淡入淡出
## 淡入淡出持续时间基于 [code]Duration[/code] 属性，默认启用
@export var fade_layout: ExportBool = ExportBool.DEFAULT

## 控件进入屏幕时的动画类型
@export var animation_enter := Anim.DEFAULT

## 控件离开屏幕时的动画类型
@export var animation_leave := Anim.DEFAULT

## 动画总持续时间（秒）
## 负值（如默认的 [code]-0.01[/code]）将使用项目设置中的默认值
@export_range(-0.01, 2.0, 0.01) var duration := -0.01

## 包含在 [code]Group[/code] 或 [code]Controls[/code] 中的每个节点之间的过渡延迟比例
## 默认值为 [code]0.5[/code][br][br]
##
## - 负值（如默认的 [code]-0.01[/code]）将使用项目设置中的默认值[br]
##
## - 延迟为 [code]0.0[/code] 表示无延迟，即所有控件同时开始和结束动画[br]
##
## - 延迟为 [code]1.0[/code] 将使每个控件等待前一个控件完成动画后再开始自己的动画[br]
##
## - [code]0.0[/code] 到 [code]1.0[/code] 之间的延迟将使控件动画交错进行，产生更流畅的效果
@export_range(-0.01, 1.0, 0.01) var delay := -0.01

## Transition curve of the animations. Same as [code]Tween.TransitionType[/code].
@export_enum(
"Default",
"LINEAR",
"SINE",
"QUINT",
"QUART",
"QUAD",
"EXPO",
"ELASTIC",
"CUBIC",
"CIRC",
"BOUNCE",
"BACK"
) var transition_type := "Default"

## Ease curve of the animations. Same as [code]Tween.EaseType[/code].
@export_enum(
"Default",
"IN",
"OUT",
"IN_OUT",
"OUT_IN"
) var ease_type := "Default"

@export_group("Target")

## The main layout node. It will be hidden and shown accordingly.
## Should be the topmost node of the current layout.
## If your don't set [code]Controls[/code] or [code]Group[/code],
## the [code]Layout[/code] itself will be animated.
## [b][color=red]Required![/color][/b]
@export var layout: NodePath

## Optional ID of layout to trigger changes on the singleton
## [code]GuiTransitions[/code] (at method parameters named [code]id[/code]).
## If empty, will be assumed as the [code]Layout[/code] node name.
@export var layout_id := ""

## Array of individual nodes to be animated.
## The order will be taken in account to apply the animation [code]Delay[/code].
## [b]If empty, a [code]Group[/code] must be set[/b].
@export var controls: Array[NodePath] = []

## A node with children controls to be animated in sequence.
## The order will be taken in account to apply the animation [code]Delay[/code].
## Example: a [code]HBoxContainer[/code] or [code]VBoxContainer[/code] with
## several buttons as children will allow to animate all buttons one by one.
## [b]If not set, [code]Controls[/code] must be selected.[/b]
@export var group: NodePath

## When [code]Animation[/code] Enter or [code]Animation Leave[/code]
## is one of the scale animations, it will center the control's
## [code]pivot_offset[/code] property.
@export var center_pivot: ExportBool = ExportBool.DEFAULT

# Private variables
## Parsed transition enum value.
var _transition := Tween.TRANS_QUAD

## Parsed ease enum value.
var _ease := Tween.EASE_IN_OUT

## Array of NodeInfo of all controls affected by transition.
var _node_infos: Array[NodeInfo] = []

## Array of all controls affected by transition.
var _controls: Array[Control] = []

## If current transition layout is being shown.
var _is_shown := false

## Parsed transition status enum value.
var _status: int = Status.OK

## If apply transitions only to layout (no group or controls set).
var _layout_only := false

## Main control affected by this transition.
@onready var _layout: Control = get_node(layout) if layout else null

## Control containing child controls affected by this transition.
@onready var _group: Control = get_node(group) if group else null

## Tweener used by this transition to perform animations.
@onready var _tween: Tween


# Built-in overrides
func _ready() -> void:
	if Engine.is_editor_hint():
		return

	_get_custom_settings()
	var temp_tween := create_tween()
	temp_tween.tween_interval(0.1)
	_transition = temp_tween.get("TRANS_" + transition_type)
	_ease = temp_tween.get("EASE_" + ease_type)

	if _transition_valid():
		if not layout_id:
			layout_id = _layout.name

		if not GuiTransitions._layouts.has(layout_id):
			GuiTransitions._layouts[layout_id] = []

		GuiTransitions._layouts[layout_id].push_back(self)

		_get_node_infos()

		# 关键修复：立即处理初始可见性，防止闪烁
		if not auto_start:
			# 不自动开始：立即将layout设置为不可见，等待手动调用show()
			# 这防止了节点在动画开始前就显示出来，避免闪烁
			_layout.visible = false
			# 同时立即隐藏所有控制的节点
			for node_info in _node_infos:
				node_info._set_node_and_children_alpha(node_info.node, 0.0)

		if fade_layout:
			_layout.modulate.a = 0.0

		if auto_start:
			# 自动开始：如果layout可见，则开始动画
			if _layout.visible:
				await get_tree().process_frame
				_show()

	else:
		push_error("Invalid GuiTransition configuration: %s" % self.get_path())
		queue_free()


# Invalidates existing tween and creates a new one.
func _init_tween() -> void:
	if _tween and _tween.is_valid():
		_tween.kill()

	_tween = create_tween()


# Remove reference from singleton.
func _exit_tree() -> void:
	# 清理所有NodeInfo的SubViewport包裹结构
	for node_info in _node_infos:
		if node_info.needs_viewport_wrapper:
			node_info.cleanup_viewport_wrapper()

	var layouts: Array = GuiTransitions._layouts.get(layout_id, [])

	if not layouts:
		return

	var index := layouts.find(self)

	if index < 0:
		return

	layouts.remove_at(index)

	if not layouts.size():
		GuiTransitions._layouts.erase(layout_id)


# Private methods
## Get custom settings from project settings and apply to current instance.
func _get_custom_settings() -> void:
	var exported_bools := ["auto_start", "fade_layout", "center_pivot"]
	var exported_strings := ["transition_type", "ease_type"]
	var exported_anims := ["animation_enter", "animation_leave"]
	var exported_floats := ["duration", "delay"]

	for setting in DefaultValues.DEFAULT_SETTINGS:
		if not ProjectSettings.has_setting(setting["name"]):
			push_warning("GUI Transition setting not found on Project Settings: " + setting["name"])
			push_warning("Try disabling and re-enabling the addon to re-add missing settings")

		var prop_name: String = Array(setting["name"].split("/")).back()
		var default_value = _round_if_float(setting["value"])
		var settings_value = _round_if_float(ProjectSettings.get_setting(setting["name"]))
		var current_value = _round_if_float(self.get(prop_name))
		var result := {}

		if prop_name in exported_bools:
			result = _process_bool_value(current_value, settings_value, default_value)
			current_value = result.get("value")

		elif prop_name in exported_strings:
			result = _process_string_value(current_value, settings_value, default_value)
			current_value = result.get("value")

		elif prop_name in exported_anims:
			result = _process_anim_value(current_value, settings_value, default_value)
			current_value = result.get("value")

		elif prop_name in exported_floats:
			result = _process_float_value(current_value, settings_value, default_value)
			current_value = result.get("value")

		if result.get("use_default"):
			self.set(prop_name, settings_value if settings_value != null else default_value)


## Process ExportBool enum value (default, true and false).
func _process_bool_value(value: int, settings_value: bool, default_value: bool) -> Dictionary:
	var fallback_value = settings_value if settings_value != null else default_value

	if value == ExportBool.DEFAULT:
		return _get_result_dict(fallback_value, true)

	return _get_result_dict(value == ExportBool.TRUE, false)


## Process value from string dropdown (default or other).
func _process_string_value(value: String, settings_value: String, default_value: String) -> Dictionary:
	var fallback_value = settings_value if settings_value != null else default_value

	if value.to_lower() == "default":
		return _get_result_dict(fallback_value, true)

	return _get_result_dict(value if value else fallback_value, false)


## Process value from float range.
func _process_float_value(value: float, settings_value: float, default_value: float) -> Dictionary:
	var fallback_value = settings_value \
	if settings_value != null and settings_value >= 0.0 \
	else default_value

	if value < 0.0:
		return _get_result_dict(fallback_value, true)

	return _get_result_dict(value, false)


## Process Anim enum value (default or animation names).
func _process_anim_value(value: int, settings_value: int, default_value: int) -> Dictionary:
	var fallback_value = settings_value if settings_value != null else default_value

	if value == Anim.DEFAULT:
		return _get_result_dict(fallback_value, true)

	return _get_result_dict(value, false)


## Get result dict of parsed setting value.
func _get_result_dict(value, use_default: bool) -> Dictionary:
	return {
		"use_default": use_default,
		"value": value,
	}


## Handles the singleton go_to calls.
func _go_to(id := "", function = null):
	if not id:
		return

	if _transition_valid() and _layout.visible:
		if id != layout_id:
			_hide("", function)
			await _tween.finished
			GuiTransitions._for_each_layout("_show", [id])
		else:
			GuiTransitions._for_each_layout("_show", [id])


## Handles the singleton update calls.
func _update(function = null):
	if _transition_valid() and _layout.visible:

		_hide(layout_id, function)
		await _tween.finished
		_show(layout_id)


## Handles the singleton show calls.
func _show(id := ""):
	if _transition_valid() and (not id or id == layout_id) and _status == Status.OK:
		_layout.visible = true
		_status = Status.SHOWING

		_init_tween()
		_fade_in_layout()

		for _node_info in _node_infos:
			var node_info: NodeInfo = _node_info

			if animation_enter == Anim.FADE:
				_fade_in(node_info)
			elif animation_enter in [Anim.SCALE, Anim.SCALE_HORIZONTAL, Anim.SCALE_VERTICAL]:
				_scale_in(node_info)
			else:
				_slide_in(node_info)

		await _tween.finished
		_is_shown = true
		_status = Status.OK

		if GuiTransitions.is_shown(layout_id):
			GuiTransitions.show_completed.emit()


## Handles the singleton hide calls.
func _hide(id := "", function = null):
	if _transition_valid() and _layout.visible and (not id or id == layout_id) and _status == Status.OK:
		_status = Status.HIDING

		_init_tween()
		_fade_out_layout()

		for node_info in _node_infos:
			if animation_leave == Anim.FADE:
				_fade_out(node_info)
			elif animation_leave in [Anim.SCALE, Anim.SCALE_HORIZONTAL, Anim.SCALE_VERTICAL]:
				_scale_out(node_info)
			else:
				_slide_out(node_info)

		await _tween.finished

		if typeof(function) == TYPE_CALLABLE:
			(function as Callable).call()

		_layout.visible = false
		_is_shown = false
		_status = Status.OK

		if GuiTransitions.is_hidden(layout_id):
			GuiTransitions.hide_completed.emit()


# Abstraction methods
## Returns if it's possible to perform transition.
func _transition_valid() -> bool:
	if not _layout:
		push_warning("A valid layout must be set on GuiTransition: %s" % get_path())
		return false

	return true


## Performs the slide in transition.
func _slide_in(node_info: NodeInfo):
	print("=== 🎬 开始滑动进入动画分析 ===")
	print("节点名称：", node_info.name)
	print("动画类型：", animation_enter, " (SLIDE_LEFT=0)")
	print("需要SubViewport包裹：", node_info.needs_viewport_wrapper)
	print("使用直接shader：", node_info.uses_direct_shader)

	# 🔍 闪烁分析：记录动画开始前的节点状态
	print("--- 📊 动画开始前节点状态 ---")
	print("  visible:", node_info.node.visible)
	print("  modulate.a:", node_info.node.modulate.a)
	print("  position:", node_info.node.position)
	print("  global_position:", node_info.node.global_position)
	print("  size:", node_info.node.size)
	print("  帧号:", Engine.get_process_frames())
	print("--------------------------------")

	# 🚨 关键修复：在任何其他操作之前立即隐藏节点
	print("🔧 立即隐藏节点以防止闪烁...")
	node_info._set_node_and_children_alpha(node_info.node, 0.0)
	print("  透明度设置完成，modulate.a:", node_info.node.modulate.a)

	# 性能优化：根据需要创建SubViewport包裹或初始化直接shader
	if node_info.needs_viewport_wrapper:
		print("🏗️ 创建SubViewport包裹...")
		print("  包裹创建前 - visible:", node_info.node.visible, ", modulate.a:", node_info.node.modulate.a)
		await node_info._ensure_viewport_wrapper_created()
		print("  包裹创建后 - visible:", node_info.node.visible, ", modulate.a:", node_info.node.modulate.a)
		print("✅ SubViewport包裹创建完成")
	elif node_info.uses_direct_shader:
		print("🎨 初始化直接shader...")
		node_info._ensure_direct_shader_initialized()
		print("✅ 直接shader初始化完成")

	node_info.init_tween()
	node_info.reset_scale()

	# 🔍 再次确认透明度设置
	print("🔧 确认透明度设置...")
	node_info._set_node_and_children_alpha(node_info.node, 0.0)
	print("  最终透明度确认，modulate.a:", node_info.node.modulate.a)

	# 立即设置节点到起始偏移位置，避免闪烁
	if not _layout_only:
		# 计算起始偏移量（相对于节点的正常位置）
		var start_offset = node_info.get_target_position(animation_enter)
		print("📐 计算起始偏移量：", start_offset)
		# 在动画开始前就设置到起始偏移位置
		node_info.set_visual_offset(start_offset)
		print("📍 已设置起始偏移位置")

		# 🔍 验证设置后的状态
		print("--- 📊 偏移设置后节点状态 ---")
		print("  visible:", node_info.node.visible)
		print("  modulate.a:", node_info.node.modulate.a)
		print("  position:", node_info.node.position)
		print("  帧号:", Engine.get_process_frames())
		print("--------------------------------")

	_fade_in_node(node_info)

	if node_info.delay:
		node_info.tween.tween_interval(node_info.delay)

	if _layout_only:
		var anchor_x := "anchor_left" if animation_enter in [Anim.SLIDE_LEFT, Anim.SLIDE_RIGHT] else "anchor_top"
		var anchor_y := "anchor_right" if animation_enter in [Anim.SLIDE_LEFT, Anim.SLIDE_RIGHT] else "anchor_bottom"
		var target_anchor := node_info.get_target_anchor(animation_enter)
		node_info.node.set(anchor_x, target_anchor.x)
		node_info.node.set(anchor_y, target_anchor.y)
		node_info.reset_anchors("v" if animation_enter in [Anim.SLIDE_LEFT, Anim.SLIDE_RIGHT] else "h")

		node_info.tween\
		.set_trans(_transition)\
		.set_ease(_ease)\
		.tween_property(
			node_info.node,
			anchor_x, 0.0,
			node_info.duration
		)
		node_info.tween\
		.parallel()\
		.set_trans(_transition)\
		.set_ease(_ease)\
		.tween_property(
			node_info.node,
			anchor_y, 1.0,
			node_info.duration
		)
	else:
		# 执行从起始偏移到零偏移的动画（回到正常位置）
		var start_offset = node_info.get_target_position(animation_enter)
		var end_offset = Vector2.ZERO  # 零偏移意味着回到正常位置
		print("开始滑动动画：从", start_offset, "到", end_offset)
		print("动画持续时间：", node_info.duration, "秒")

		node_info.tween\
		.set_trans(_transition)\
		.set_ease(_ease)\
		.tween_method(
			node_info.set_visual_offset,
			start_offset,
			end_offset,
			node_info.duration
		)
		print("滑动动画tween已设置")

	node_info.unset_clickable()
	print("等待滑动动画完成...")
	await node_info.tween.finished
	print("滑动动画完成！")
	node_info.revert_clickable()

	# 性能优化：根据动画类型进行相应的清理
	if node_info.needs_viewport_wrapper:
		# SubViewport包裹：直接清理包裹结构，避免视觉间隙
		print("清理SubViewport包裹结构...")
		node_info.cleanup_viewport_wrapper()
		print("SubViewport包裹结构清理完成")
	elif node_info.uses_direct_shader:
		# 直接shader：清理shader材质
		print("清理直接shader...")
		node_info.cleanup_direct_shader()
		print("直接shader清理完成")
	else:
		# 传统方式：清理shader参数
		print("清理传统shader参数...")
		node_info.reset_shader()
		print("传统shader参数清理完成")

	print("=== 滑动进入动画结束 ===")
	print("")


## Performs the slide out transition.
func _slide_out(node_info: NodeInfo):
	# 性能优化：根据需要创建SubViewport包裹或初始化直接shader
	if node_info.needs_viewport_wrapper:
		await node_info._ensure_viewport_wrapper_created()
	elif node_info.uses_direct_shader:
		node_info._ensure_direct_shader_initialized()

	node_info.init_tween()
	node_info.reset_scale()
	node_info.node.custom_minimum_size = Vector2(1, 1)
	node_info.node.custom_minimum_size = Vector2.ZERO

	if node_info.delay:
		node_info.tween.tween_interval(node_info.delay)

	if _layout_only:
		var anchor_x := "anchor_left" if animation_leave in [Anim.SLIDE_LEFT, Anim.SLIDE_RIGHT] else "anchor_top"
		var anchor_y := "anchor_right" if animation_leave in [Anim.SLIDE_LEFT, Anim.SLIDE_RIGHT] else "anchor_bottom"
		var target_anchor := node_info.get_target_anchor(animation_leave)
		node_info.reset_anchors("both")

		node_info.tween\
		.set_trans(_transition)\
		.set_ease(_ease)\
		.tween_property(
			node_info.node,
			anchor_x, target_anchor.x,
			node_info.duration
		)
		node_info.tween\
		.parallel()\
		.set_trans(_transition)\
		.set_ease(_ease)\
		.tween_property(
			node_info.node,
			anchor_y, target_anchor.y,
			node_info.duration
		)
	else:
		# 执行从零偏移到目标偏移的动画（移动到屏幕外）
		var start_offset = Vector2.ZERO  # 从正常位置开始
		var end_offset = node_info.get_target_position(animation_leave)

		node_info.tween\
		.set_trans(_transition)\
		.set_ease(_ease)\
		.tween_method(
			node_info.set_visual_offset,
			start_offset,
			end_offset,
			node_info.duration
		)

	node_info.unset_clickable()
	await node_info.tween.finished
	node_info._set_node_and_children_alpha(node_info.node, 0.0)

	# 性能优化：根据动画类型进行相应的清理
	if node_info.needs_viewport_wrapper:
		node_info.cleanup_viewport_wrapper()
	elif node_info.uses_direct_shader:
		node_info.cleanup_direct_shader()


## Performs the fade in transition.
func _fade_in(node_info: NodeInfo):
	node_info.init_tween()
	node_info.set_visual_offset(Vector2.ZERO)  # 确保在正确位置
	node_info.reset_anchors("both")
	node_info.reset_scale()
	node_info._set_node_and_children_alpha(node_info.node, 0.0)

	if node_info.delay:
		node_info.tween.tween_interval(node_info.delay)

	# 使用tween_method来同时处理节点及其子节点的透明度
	node_info.tween\
	.set_trans(_transition)\
	.set_ease(_ease)\
	.tween_method(
		func(alpha: float): node_info._set_node_and_children_alpha(node_info.node, alpha),
			0.0,
			1.0,
			node_info.duration
	)

	node_info.unset_clickable()
	await node_info.tween.finished
	node_info.revert_clickable()

	# 清理shader参数
	node_info.reset_shader()

	# 清理SubViewport包裹结构（如果有的话）
	if node_info.needs_viewport_wrapper:
		node_info.cleanup_viewport_wrapper()


## Performs the fade out transition.
func _fade_out(node_info: NodeInfo):
	node_info.init_tween()
	node_info.reset_anchors("both")
	node_info.reset_scale()

	if node_info.delay:
		node_info.tween.tween_interval(node_info.delay)

	node_info.tween\
	.set_trans(_transition)\
	.set_ease(_ease)\
	.tween_property(node_info.node, "modulate:a", 0.0, node_info.duration)

	node_info.unset_clickable()


## Performs the scale in transition.
func _scale_in(node_info: NodeInfo):
	# 性能优化：根据需要创建SubViewport包裹或初始化直接shader
	if node_info.needs_viewport_wrapper:
		await node_info._ensure_viewport_wrapper_created()
	elif node_info.uses_direct_shader:
		node_info._ensure_direct_shader_initialized()

	node_info.init_tween()
	node_info.set_visual_offset(Vector2.ZERO)  # 确保在正确位置
	node_info.reset_anchors("both")

	node_info._set_node_and_children_alpha(node_info.node, 0.0)
	_fade_in_node(node_info)

	node_info.tween.tween_callback(node_info.set_pivot_to_center)

	# 获取目标缩放值
	var target_scale = node_info.get_target_scale(animation_enter)

	# 设置初始shader缩放状态
	node_info.tween.tween_callback(node_info.set_visual_scale.bind(target_scale))

	if node_info.delay:
		node_info.tween.tween_interval(node_info.delay)

	# 使用shader进行缩放动画
	node_info.tween\
	.set_trans(_transition)\
	.set_ease(_ease)\
	.tween_method(
		node_info.set_visual_scale,
		target_scale,
		node_info.initial_scale,
		node_info.duration
	)

	node_info.unset_clickable()

	await node_info.tween.finished
	node_info.revert_clickable()

	# 性能优化：根据动画类型进行相应的清理
	if node_info.needs_viewport_wrapper:
		# SubViewport包裹：直接清理包裹结构，避免视觉间隙
		node_info.cleanup_viewport_wrapper()
	elif node_info.uses_direct_shader:
		# 直接shader：清理shader材质
		node_info.cleanup_direct_shader()
	else:
		# 传统方式：清理shader参数
		node_info.reset_shader()


## Performs the scale out transition.
func _scale_out(node_info: NodeInfo):
	# 性能优化：根据需要创建SubViewport包裹或初始化直接shader
	if node_info.needs_viewport_wrapper:
		await node_info._ensure_viewport_wrapper_created()
	elif node_info.uses_direct_shader:
		node_info._ensure_direct_shader_initialized()

	node_info.init_tween()
	node_info.reset_anchors("both")
	node_info.reset_scale()

	node_info.tween.tween_callback(node_info.set_pivot_to_center)

	# 设置初始shader缩放状态
	node_info.tween.tween_callback(node_info.set_visual_scale.bind(node_info.initial_scale))

	if node_info.delay:
		node_info.tween.tween_interval(node_info.delay)

	# 获取目标缩放值
	var target_scale = node_info.get_target_scale(animation_leave)

	# 使用shader进行缩放动画
	node_info.tween\
	.set_trans(_transition)\
	.set_ease(_ease)\
	.tween_method(
		node_info.set_visual_scale,
		node_info.initial_scale,
		target_scale,
		node_info.duration
	)

	node_info.unset_clickable()
	await node_info.tween.finished
	node_info._set_node_and_children_alpha(node_info.node, 0.0)

	# 性能优化：根据动画类型进行相应的清理
	if node_info.needs_viewport_wrapper:
		node_info.cleanup_viewport_wrapper()
	elif node_info.uses_direct_shader:
		node_info.cleanup_direct_shader()


## Gradually fade in the whole layout along with individual transitions.
func _fade_in_layout() -> void:
	if not fade_layout or _layout_only and animation_enter == Anim.FADE:
		_tween.tween_interval(duration)
		return

	_tween.tween_property(_layout, "modulate:a", 1.0, duration)


## Gradually fade out the whole layout along with individual transitions.
func _fade_out_layout() -> void:
	if not fade_layout or _layout_only and animation_leave == Anim.FADE:
		_tween.tween_interval(duration)
		return

	_tween.tween_property(_layout, "modulate:a", 0.0, duration)


## Fix of node pop-in in some cases.
func _fade_in_node(node_info: NodeInfo) -> void:
	print("🌅 开始淡入动画")
	print("  节点名称:", node_info.name)
	print("  当前透明度:", node_info.node.modulate.a)
	print("  当前可见性:", node_info.node.visible)
	print("  帧号:", Engine.get_process_frames())

	var node_duration := max(node_info.duration / 3.0, 0.09)
	print("  淡入持续时间:", node_duration, "秒")

	var tween := node_info.node.create_tween()

	if node_info.delay:
		print("  延迟时间:", node_info.delay, "秒")
		tween.tween_interval(node_info.delay)

	print("🎭 开始透明度动画：0.0 → 1.0")
	# 使用回调函数来同时处理节点及其子节点的透明度
	tween.tween_method(
		func(alpha: float):
			print("  🎨 淡入进度 - alpha:", alpha, ", 帧号:", Engine.get_process_frames())
			node_info._set_node_and_children_alpha(node_info.node, alpha),
			0.0,
			1.0,
			node_duration
	)

	print("✅ 淡入动画tween已设置")


## Get nodes from group or array of node paths set by the user.
func _get_nodes_from_containers() -> Array[Control]:
	_controls.clear()

	for node_path in controls:
		var node: Node = get_node(node_path) if node_path else null

		if node:
			_controls.push_back(node)

	var nodes: Array = _controls if _controls.size() \
	else _group.get_children() if _group \
	else [_layout]
	_layout_only = _layout and not _controls.size() and not _group
	var filtered_nodes: Array[Control] = []

	for n in nodes:
		var node: Node = n

		# 接受所有Control类型的节点，包括基础Control类
		if node and node.is_class("Control"):
			filtered_nodes.push_back(node)

	return filtered_nodes


## Get children nodes from group children or controls array.
func _get_node_infos() -> void:
	var filtered_nodes := _get_nodes_from_containers()

	if not filtered_nodes.size():
		push_warning("No valid group children or controls set on GuiTransition: %s" % self.get_path())

	var base_duration := duration / filtered_nodes.size()
	var inv_delay := 1.0 - delay

	_node_infos.clear()

	for _i in filtered_nodes.size():
		var i: int = _i
		var current_delay := i * delay * base_duration
		var current_duration := base_duration + base_duration * inv_delay * 3

		if filtered_nodes.size() == 1:
			current_duration = duration

		_node_infos.push_back(NodeInfo.new(
			filtered_nodes[i],
			current_delay,
			current_duration,
			animation_enter,
			animation_leave,
			auto_start,
			center_pivot,
			_layout_only
		))


# Helper methods
## Round float value by the step of 0.01.
func _round_if_float(value):
	if typeof(value) == TYPE_FLOAT:
		return snapped(value, 0.01)

	return value
