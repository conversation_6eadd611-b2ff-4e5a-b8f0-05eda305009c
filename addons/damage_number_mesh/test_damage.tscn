[gd_scene load_steps=6 format=3 uid="uid://b0b3whtfx5fpf"]

[ext_resource type="Script" uid="uid://clfi8sp21ok7b" path="res://addons/damage_number_mesh/test_damage.gd" id="1_ldhr2"]
[ext_resource type="PackedScene" uid="uid://bjuk4xs6y84rq" path="res://addons/damage_number_mesh/damage_manager.tscn" id="2_4a5mm"]
[ext_resource type="Shader" uid="uid://dqueevxr81bom" path="res://addons/damage_number_mesh/tonOfDamageText.gdshader" id="3_gt3cc"]
[ext_resource type="Texture2D" uid="uid://ds1ko71q8f0rr" path="res://addons/damage_number_mesh/output.png" id="4_gt3cc"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_e6ubl"]
shader = ExtResource("3_gt3cc")
shader_parameter/numbersTex = ExtResource("4_gt3cc")
shader_parameter/digit_spacing = 0.0
shader_parameter/uv_inset = Vector2(0.21, 0)
shader_parameter/stroke_thickness = 3.0
shader_parameter/stroke_color = Color(0, 0, 0, 1)
shader_parameter/pattern = 0
shader_parameter/inside = false

[node name="TestDamage" type="Node2D"]
script = ExtResource("1_ldhr2")

[node name="DamageManager" parent="." instance=ExtResource("2_4a5mm")]
material = SubResource("ShaderMaterial_e6ubl")
base_scale = Vector2(0.43, 2.5)
global_acceleration = Vector2(0, 0)
initial_velocity_min = Vector2(0, -100)
initial_velocity_max = Vector2(0, -100)
lifetime = 1.0
