shader_type canvas_item;

// 将纹理类型从 sampler2DArray 修改为 sampler2D，以接受普通的2D纹理（如 nums.png）
uniform sampler2D numbersTex : filter_nearest;
// 数字之间的间距，以单个数字的宽度为单位。例如 0.1 代表 10% 的间距。
uniform float digit_spacing : hint_range(0.0, 2.0, 0.01) = 0.1;
// 内部UV缩减量，用于分别裁剪数字边缘的透明像素。
// uv_inset.x 用于左右裁剪, uv_inset.y 用于上下裁剪。
uniform vec2 uv_inset = vec2(0.0, 0.0);
// 描边效果的控制参数 (参考 stroke.gdshader)
uniform float stroke_thickness : hint_range(0.0, 10.0, 0.1) = 1.0;
uniform vec4 stroke_color : source_color = vec4(0.0, 0.0, 0.0, 1.0);
uniform int pattern : hint_range(0, 2) = 0; // 0:diamond, 1:circle, 2:square
uniform bool inside = false;

// 我们需要两个 varying 变量，来连接顶点着色器和片段着色器
varying vec4 instance_color; // 用于传递颜色
varying vec4 custom_data;    // 用于传递自定义数据（伤害数值等）

const float ENCODING_BASE = 12.0;

// 在顶点着色器中，我们从 MultiMesh 获取数据，并将其传递给片段着色器
void vertex(){
	instance_color = COLOR;          // 获取实例颜色
	custom_data = INSTANCE_CUSTOM; // 获取实例自定义数据
}

float getDigitValue(float num, int index) {
	return floor(mod(num / pow(10.0, float(index)), 10.0));
}

// 辅助函数：检查邻近像素，改编自 stroke.gdshader 以适配纹理图集
bool atlas_has_contrary_neighbor(vec2 local_uv, sampler2D atlas_tex, float p_num_width, float p_num_offset) {
	vec2 pixel_size = 1.0 / vec2(textureSize(atlas_tex, 0));
	// 将屏幕空间的描边宽度转换为局部UV空间的宽度
	vec2 local_uv_offset = vec2(pixel_size.x / p_num_width, pixel_size.y) * stroke_thickness;

	for (float i = -ceil(stroke_thickness); i <= ceil(stroke_thickness); i++) {
		float x = abs(i) > stroke_thickness ? stroke_thickness * sign(i) : i;
		float offset_y;

		if (pattern == 0) { // diamond
			offset_y = stroke_thickness - abs(x);
		} else if (pattern == 1) { // circle
			offset_y = floor(sqrt(pow(stroke_thickness + 0.5, 2) - x * x));
		} else { // square
			offset_y = stroke_thickness;
		}

		for (float j = -ceil(offset_y); j <= ceil(offset_y); j++) {
			float y = abs(j) > offset_y ? offset_y * sign(j) : j;

			// 计算邻居在局部UV空间的坐标
			vec2 neighbor_local_uv = local_uv + vec2(pixel_size.x / p_num_width, pixel_size.y) * vec2(x, y);

			bool is_neighbor_outside = any(lessThan(neighbor_local_uv, vec2(0.0))) || any(greaterThan(neighbor_local_uv, vec2(1.0)));

			bool is_neighbor_transparent;
			if (is_neighbor_outside) {
				is_neighbor_transparent = true;
			} else {
				vec2 neighbor_atlas_uv = vec2(neighbor_local_uv.x * p_num_width + p_num_offset, neighbor_local_uv.y);
				is_neighbor_transparent = texture(atlas_tex, neighbor_atlas_uv).a <= 0.0;
			}

			// 如果邻居是透明的，但描边要求是在内部（即寻找实体像素），则条件不符，反之亦然
			if (is_neighbor_transparent == inside) {
				return true;
			}
		}
	}
	return false;
}

void fragment() {
	// 使用从顶点着色器传递过来的 custom_data
	float currentNum = custom_data.x;
	float numberBit = custom_data.y;

	// --- 新的 UV 计算逻辑，支持自定义间距 ---

	// 如果只有一位数，则不存在间距。
	float spacing = (numberBit > 1.0) ? digit_spacing : 0.0;

	// 计算包含所有间距在内的总相对宽度。把单个数字的宽度看作 1.0。
	float total_relative_width = numberBit + (numberBit - 1.0) * spacing;

	// 根据 UV.x 计算当前像素在整个数字序列中的相对位置。
	float current_pos = UV.x * total_relative_width;

	// 单个“字符块”（一个数字 + 它后面的间距）的宽度。
	float cell_width = 1.0 + spacing;

	// 计算当前像素属于第几个“字符块”。
	int numIndex = int(floor(current_pos / cell_width));

	// 计算当前像素在它所属的“字符块”中的位置。
	// 使用减法代替 mod 运算，以避免边界条件下的浮点数错误。
	float pos_in_cell = current_pos - (float(numIndex) * cell_width);

	// 如果像素位置大于 1.0，说明它落在间距区域，直接丢弃该像素使其透明。
	if (pos_in_cell > 1.0) {
		discard;
	}

	// 如果没被丢弃，说明像素在数字区域内。pos_in_cell (范围 0-1) 就是该数字的水平UV。
	float digit_uv_x = pos_in_cell;

	// --- NEW LOGIC: Unpack encoded text ---
	// 修正：解码的幂次必须与编码时从右到左的顺序相匹配。
	// 编码时，最右边的字符(numIndex = numberBit - 1)对应 power = 0。
	// 因此，从左到右的 numIndex (0, 1, 2...) 对应的 power 是 (numberBit - 1 - numIndex)。
	float power = numberBit - 1.0 - float(numIndex);

	// 使用更稳健的数学方法解码，并添加一个小的 epsilon (0.001) 来补偿浮点数精度损失。
	float char_index = floor(mod(currentNum + 0.001, pow(ENCODING_BASE, power + 1.0)) / pow(ENCODING_BASE, power));

	// 计算采样纹理图集时的最终UV坐标。
	float num_width = 1.0 / 11.0; // 单个字符在纹理中占的宽度 (1/11)
	float num_offset = char_index * num_width; // 计算字符在纹理图集中的偏移量

	// --- 新增：应用UV向内缩减 ---
	// 重新映射UV坐标，实现向内裁剪效果
	float final_uv_x = digit_uv_x * (1.0 - 2.0 * uv_inset.x) + uv_inset.x;
	float final_uv_y = (1.0 - UV.y) * (1.0 - 2.0 * uv_inset.y) + uv_inset.y;
	vec2 final_local_uv = vec2(final_uv_x, final_uv_y);
	vec2 center_atlas_uv = vec2(final_local_uv.x * num_width + num_offset, final_local_uv.y);

    vec4 texture_color = texture(numbersTex, center_atlas_uv);

	// --- 全新的描边和颜色混合逻辑 ---
	if (stroke_thickness > 0.0) {
		bool is_pixel_solid = texture_color.a > 0.01;

		// 检查当前像素是否应作为描边像素被绘制
		if ((is_pixel_solid == inside) && atlas_has_contrary_neighbor(final_local_uv, numbersTex, num_width, num_offset)) {
			// 如果是描边像素，根据 'inside' 混合颜色
			vec4 final_stroke_color = stroke_color;
			if (inside) {
				// 内部描边：将描边颜色混合到原有颜色之上
				COLOR.rgb = mix(instance_color.rgb, final_stroke_color.rgb, final_stroke_color.a);
				// 透明度叠加
				COLOR.a = texture_color.a + (1.0 - texture_color.a) * final_stroke_color.a;
			} else {
				// 外部描边：将原有颜色混合到描边颜色之上
				COLOR.rgb = mix(final_stroke_color.rgb, instance_color.rgb, texture_color.a);
				COLOR.a = final_stroke_color.a + (1.0 - final_stroke_color.a) * texture_color.a;
			}
		} else {
			// 如果不是描边像素，则只显示数字本身
			COLOR.rgb = instance_color.rgb;
			COLOR.a = texture_color.a;
		}
		// 最终应用实例的淡出效果
		COLOR.a *= instance_color.a;
	} else {
		// 如果不启用描边，则使用原始的颜色混合逻辑
		COLOR.rgb = instance_color.rgb;
		COLOR.a = texture_color.a * instance_color.a;
	}
}