# damage_manager.gd
# 全局伤害数字管理器。
# 需在 "项目" -> "项目设置" -> "自动加载" 中注册为单例 (例如 "DamageManager")。
# 同时，请将该脚本挂载到一个 MultiMeshInstance2D 节点上，
# 并确保该节点的材质 (material) 和网格 (multimesh) 已正确配置。
# (可以参考 addons/damage_number/node_2d.tscn 的设置)
class_name DamageMeshManager
extends MultiMeshInstance2D


# --- 新增：字符编码配置 ---
const CHAR_MAP = {
	"0": 0, "1": 1, "2": 2, "3": 3, "4": 4, "5": 5, "6": 6, "7": 7, "8": 8, "9": 9,
	"!": 10
}
const ENCODING_BASE = 12.0 # 基数必须大于等于 CHAR_MAP 的大小


# --- 可在检查器中调整的配置 ---
@export var base_scale: Vector2 = Vector2(1.0, 1.0)

@export_group("Motion")
# 施加于所有实例的全局加速度。可用于模拟重力 (0, 980) 或风力。
@export var global_acceleration: Vector2 = Vector2(0, 980)
# 每个实例出生时，在其全局加速度基础上增加的额外随机加速度范围。
@export var random_acceleration_min: Vector2 = Vector2.ZERO
@export var random_acceleration_max: Vector2 = Vector2.ZERO
# 阻尼/减速的随机范围。值越大，减速越快。
@export var damping_min: float = 0.0
@export var damping_max: float = 0.0

@export_group("Initial Velocity")
# 数字初始飞出速度的随机范围
@export var initial_velocity_min: Vector2 = Vector2(-200, -800)
@export var initial_velocity_max: Vector2 = Vector2(200, -400)

@export_group("Animation")
# 同时存在的最大数字数量 (对象池大小)
@export var max_damage_text_num = 1000
# 数字的生命周期（秒）
@export var lifetime: float = 1.5
# 控制数字在生命周期内缩放变化的曲线资源
@export var scale_curve: Curve
# 控制数字在生命周期内透明度变化的曲线资源
@export var alpha_curve: Curve


# --- 私有变量 ---
var _digit_spacing := 0.1 # 从材质中缓存的间距值


# --- 内部数据结构 (Internal Data Structure) ---
class DamageNumber:
	var text: String
	var encoded_text: float
	var transform: Transform2D
	var velocity: Vector2
	var color: Color
	var time: float = 0.0 # 跟踪实例已存在的时间
	var acceleration: Vector2 = Vector2.ZERO # 存储实例的加速度
	var damping: float = 0.0 # 存储实例的阻尼
	var base_instance_scale: float = 1.0 # 新增：用于独立控制该实例的基础缩放

	func _init(p_text: String, p_encoded_text: float):
		text = p_text
		encoded_text = p_encoded_text


# --- 状态变量 (State Variables) ---
var _damage_numbers: Array[DamageNumber] = []


# --- Godot 生命周期方法 ---

func _ready():
	# 创建兼容的四边形网格以替代 QuadMesh
	_create_compatible_quad_mesh()

	multimesh.instance_count = max_damage_text_num
	multimesh.visible_instance_count = 0
	# 在准备就绪时，从材质中读取并缓存间距值，以提高性能
	if material is ShaderMaterial:
		var spacing = material.get_shader_parameter("digit_spacing")
		if spacing != null:
			_digit_spacing = spacing


func _process(delta: float):
	_update_damage_numbers(delta)




# --- 公共方法 (Public API) ---

## 在指定位置生成一个伤害文本。可以从其他脚本调用此方法。
## @pos: 生成数字的本地2D坐标
## @text: 要显示的文本字符串 (例如 "123" or "135!")
## @color: 要显示的颜色
## @p_scale: (可选) 该伤害文本的独立缩放系数, 类似于字体大小。
func spawn_damage_number(pos: Vector2, text: String, color: Color, p_scale: float = 1.0):
	# 如果活动数字的数量达到上限，则移除最旧的一个以复用实例
	if _damage_numbers.size() >= max_damage_text_num:
		_damage_numbers.pop_front()
	
	var encoded_value = _encode_text(text)
	var new_number = DamageNumber.new(text, encoded_value)
	new_number.base_instance_scale = p_scale # 存储独立缩放值
	
	# 使用辅助函数计算包含间距的正确基础缩放，并应用实例独立缩放
	var initial_scale = _get_base_scale(text.length()) * p_scale
	
	# 初始化Transform，使其与更新逻辑一致
	new_number.transform = Transform2D()
	new_number.transform.origin = pos
	new_number.transform.x = Vector2.RIGHT * initial_scale.x
	new_number.transform.y = Vector2.DOWN * initial_scale.y
	
	# 设置随机的初始速度
	new_number.velocity = Vector2(
		randf_range(initial_velocity_min.x, initial_velocity_max.x),
		randf_range(initial_velocity_min.y, initial_velocity_max.y)
	)
	
	# 为实例设置最终的加速度和阻尼
	var random_acceleration = Vector2(
		randf_range(random_acceleration_min.x, random_acceleration_max.x),
		randf_range(random_acceleration_min.y, random_acceleration_max.y)
	)
	new_number.acceleration = global_acceleration + random_acceleration
	new_number.damping = randf_range(damping_min, damping_max)
	
	new_number.color = color
	_damage_numbers.append(new_number)


# --- 私有方法 (Private Methods) ---

## 创建兼容微信小游戏的四边形网格，替代 QuadMesh
func _create_compatible_quad_mesh():
	# 检查当前网格是否已经是有效的网格
	if multimesh.mesh != null and multimesh.mesh.get_surface_count() > 0:
		return

	# 创建 ArrayMesh 作为 QuadMesh 的替代
	var array_mesh = ArrayMesh.new()
	var arrays = []
	arrays.resize(Mesh.ARRAY_MAX)

	# 定义四边形的顶点 (96x32 大小，与原 QuadMesh 相同)
	var vertices = PackedVector3Array()
	vertices.push_back(Vector3(-48, -16, 0))  # 左下
	vertices.push_back(Vector3(48, -16, 0))   # 右下
	vertices.push_back(Vector3(48, 16, 0))    # 右上
	vertices.push_back(Vector3(-48, 16, 0))   # 左上

	# 定义 UV 坐标
	var uvs = PackedVector2Array()
	uvs.push_back(Vector2(0, 1))  # 左下
	uvs.push_back(Vector2(1, 1))  # 右下
	uvs.push_back(Vector2(1, 0))  # 右上
	uvs.push_back(Vector2(0, 0))  # 左上

	# 定义法线
	var normals = PackedVector3Array()
	for i in range(4):
		normals.push_back(Vector3(0, 0, 1))

	# 定义索引（两个三角形组成四边形）
	var indices = PackedInt32Array()
	indices.push_back(0)
	indices.push_back(1)
	indices.push_back(2)
	indices.push_back(0)
	indices.push_back(2)
	indices.push_back(3)

	# 设置数组
	arrays[Mesh.ARRAY_VERTEX] = vertices
	arrays[Mesh.ARRAY_TEX_UV] = uvs
	arrays[Mesh.ARRAY_NORMAL] = normals
	arrays[Mesh.ARRAY_INDEX] = indices

	# 创建表面
	array_mesh.add_surface_from_arrays(Mesh.PRIMITIVE_TRIANGLES, arrays)

	# 设置到 MultiMesh
	multimesh.mesh = array_mesh

func _encode_text(text: String) -> float:
	var encoded_value: float = 0.0
	var power = 0
	# 我们需要从右到左遍历字符串来正确计算基于幂次的值
	for char_str in text.reverse():
		if not CHAR_MAP.has(char_str):
			push_warning("Character '%s' is not in CHAR_MAP and will be skipped." % char_str)
			continue
		var char_index = CHAR_MAP[char_str]
		encoded_value += char_index * pow(ENCODING_BASE, power)
		power += 1
	return encoded_value


func _update_damage_numbers(delta: float):
	if _damage_numbers.is_empty():
		multimesh.visible_instance_count = 0
		return

	var viewport_height = get_viewport_rect().size.y
	var i = 0
	
	while i < _damage_numbers.size():
		var num = _damage_numbers[i]
		
		# 更新存在时间
		num.time += delta
		
		# 如果超时或移出屏幕，则移除实例
		if num.time >= lifetime or num.transform.origin.y > viewport_height:
			# 将原来的“交换并弹出”优化替换为 remove_at，以严格保证绘制顺序。
			# 这可以防止新实例被移动到旧实例的低索引位置，从而被绘制在下方。
			_damage_numbers.remove_at(i)
			continue

		# --- 更新速度 ---
		num.velocity += num.acceleration * delta
		num.velocity = num.velocity.lerp(Vector2.ZERO, num.damping * delta)

		# 更新物理位置
		num.transform.origin += num.velocity * delta
		
		# --- 根据曲线更新缩放和透明度 ---
		var progress = num.time / lifetime

		# 1. 更新缩放
		var scale_multiplier = 1.0
		if scale_curve and scale_curve.get_point_count() > 0:
			scale_multiplier = scale_curve.sample_baked(progress)
			
		# 使用辅助函数获取正确的基础宽度
		var base_scale_vec = _get_base_scale(num.text.length())
		
		# 应用基础缩放、动画曲线缩放和实例独立缩放
		var final_scale = base_scale_vec * scale_multiplier * num.base_instance_scale
			
		# 安全地更新 Transform 的缩放，同时保留位置和旋转
		num.transform.x = Vector2.RIGHT * final_scale.x
		num.transform.y = Vector2.DOWN * final_scale.y

		# 2. 更新透明度
		var current_color = num.color
		if alpha_curve and alpha_curve.get_point_count() > 0:
			current_color.a = alpha_curve.sample_baked(progress)
		
		# 将更新后的数据设置到对应的 MultiMesh 实例上
		multimesh.set_instance_transform_2d(i, num.transform)
		multimesh.set_instance_color(i, current_color)
		multimesh.set_instance_custom_data(i, Color(num.encoded_text, float(num.text.length()), 0.0, 0.0))
		
		i += 1

	multimesh.visible_instance_count = _damage_numbers.size()


# --- 新增的辅助函数 ---

## 根据字符数和缓存的间距值，计算伤害文本的基础缩放尺寸。
func _get_base_scale(char_count: int) -> Vector2:
	var count = float(char_count)
	# 如果位数大于1，则应用间距计算
	var spacing_units = _digit_spacing if count > 1.0 else 0.0
	# 计算包含所有间距在内的总相对宽度
	var relative_width = count + (count - 1.0) * spacing_units
	# 乘以基础宽度得到最终的缩放X值
	var scale_x = relative_width * base_scale.x
	return Vector2(scale_x, base_scale.y)
