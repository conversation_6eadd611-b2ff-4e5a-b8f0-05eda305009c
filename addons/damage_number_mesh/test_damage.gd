extends Node2D

@onready var damage_manager = $DamageManager

# 按住鼠标时生成伤害数字的间隔（秒）
@export var spawn_interval: float = 0.001

# --- 状态变量 ---
# 用于跟踪鼠标是否被按住
var _is_mouse_pressed: bool = false
# 用于在 _process 中累计时间
var _time_since_last_spawn: float = 0.0


func _input(event: InputEvent):
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT:
			_is_mouse_pressed = event.is_pressed()
			# 如果是刚按下的那一刻，为了立即响应，可以先生成一个
			if _is_mouse_pressed:
				_spawn_one_damage_number()
				# 重置计时器，以保证按下的瞬间和第一次定时生成之间有完整的间隔
				_time_since_last_spawn = 0.0
		elif event.button_index == MOUSE_BUTTON_RIGHT and event.is_pressed():
			# 右键点击，生成一个彩色的 "135!" 字符串作为示例
			var random_color = Color(randf_range(0.0, 1.0), randf_range(0.0, 1.0), randf_range(0.0, 1.0))
			damage_manager.spawn_damage_number(get_local_mouse_position(), "135!", random_color, 1.5)


func _process(delta: float):
	if _is_mouse_pressed:
		# 累加时间
		_time_since_last_spawn += delta
		
		# 如果累计时间超过了生成间隔，就生成数字
		# 使用 while 循环可以确保在低帧率下也能跟上生成速度
		while _time_since_last_spawn >= spawn_interval:
			_spawn_one_damage_number()
			# 减去一个间隔时间，而不是直接清零，以保持节拍的准确性
			_time_since_last_spawn -= spawn_interval
	else:
		# 如果鼠标松开，则重置计时器
		_time_since_last_spawn = 0.0


# 封装的生成函数，避免代码重复
func _spawn_one_damage_number():
	var random_amount = randi_range(0, 200)
	var random_color = Color(randf_range(0.0, 1.0), randf_range(0.0, 1.0), randf_range(0.0, 1.0))
	damage_manager.spawn_damage_number(get_local_mouse_position(), str(random_amount), random_color)
