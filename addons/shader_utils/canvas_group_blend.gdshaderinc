// canvas_group_blend.gdshaderinc

// 包含 screen_texture uniform 的定义
uniform sampler2D screen_texture : hint_screen_texture, repeat_disable, filter_nearest;

/*
 * @brief 与 CanvasGroup 的背景缓冲区进行混合
 * 
 * 该函数从 screen_texture 中获取背景色，进行预乘Alpha校正，
 * 然后将传入的特效颜色（effect_color）与背景进行混合。
 *
 * @param effect_color  vec4  - 你生成的特效颜色（应包含alpha值）。
 * @param screen_uv     vec2  - 通常直接传入 SCREEN_UV。
 * @return              vec4  - 最终混合后可以赋给 COLOR 的颜色。
 */
vec4 blend_canvas_background(vec4 effect_color, vec2 screen_uv) {
	// 1. 读取 CanvasGroup 子节点的渲染结果
	vec4 background = texture(screen_texture, screen_uv);

	// 2. 预乘Alpha校正: 以获得正确的原始颜色
	if (background.a > 0.0001) {
		background.rgb /= background.a;
	}
	
	// 3. 混合：这里使用加法混合，模拟发光效果
	// effect_color.a 控制了混合的强度
	vec3 blended_rgb = background.rgb + effect_color.rgb * effect_color.a;
	
	// 4. 返回最终颜色，alpha 值通常使用背景的 alpha
	return vec4(blended_rgb, background.a);
} 