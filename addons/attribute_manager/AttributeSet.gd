@tool
class_name AttributeSet
extends Resource

@export var attributes: Dictionary[StringName, Attribute]: set = setter_attributes

## 运行时数据
var attributes_runtime_dict: Dictionary[StringName, Attribute] = {}
var is_initializing := false
## 记录依赖属性关联下的其它属性
## 当该依赖属性发生变化时，通知关联属性更新数值
## Key: StringName 依赖属性名
## Value: Array[Attribute] 关联属性
var derived_attributes_dict: Dictionary = {}


#region setter
func setter_attributes(v):
	attributes = v

	is_initializing = true
	_create_runtime_attributes()
	_create_derived_attributes()
	_init_runtime_attributes()
	is_initializing = false


#endregion


#region 外部函数
## 由外部驱动（AttributeComponent）
func run_process(delta: float):
	for _name in attributes_runtime_dict:
		var runtime_attribute: Attribute = attributes_runtime_dict[_name]
		runtime_attribute.run_process(delta)


func find_attribute(attribute_name) -> Attribute:
	var key: StringName = StringName(attribute_name)
	if attributes_runtime_dict.has(key):
		return attributes_runtime_dict[key]
	push_error("cann't find attribute | attribute_name: %s" % attribute_name)
	return null


func add_attribute(attribute_name, attribute: Attribute) -> void:
	if has_attribute(attribute_name):
		push_warning("attribute already exists | attribute_name: %s" % attribute_name)
		return

	var key: StringName      = StringName(attribute_name)
	attributes[key] = attribute
	var duplicated_attribute: Attribute = attribute.duplicate(true) as Attribute
	duplicated_attribute.attribute_name = key
	duplicated_attribute.attribute_set = weakref(self)
	attributes_runtime_dict[key] = duplicated_attribute
	duplicated_attribute.attribute_changed.connect(_on_attribute_changed)

	_create_derived_attributes()

	var base_value: float = duplicated_attribute.get_base_value()
	# Lazy-compute: 只设置原始值，不立刻触发公式计算
	duplicated_attribute.computed_value = base_value


func has_attribute(attribute_name) -> bool:
	return attributes_runtime_dict.has(StringName(attribute_name))


#endregion


#region 内部函数
func _create_runtime_attributes():
	attributes_runtime_dict.clear()
	for attr_name in attributes:
		var attr: Attribute = attributes[attr_name]
		if not is_instance_valid(attr):
			continue

		var duplicated_attribute: Attribute = attr.duplicate(true) as Attribute
		duplicated_attribute.attribute_name = StringName(attr_name)
		duplicated_attribute.attribute_set = weakref(self)
		attributes_runtime_dict[attr_name] = duplicated_attribute
		## 监听属性变化
		duplicated_attribute.attribute_changed.connect(_on_attribute_changed)


func _create_derived_attributes():
	derived_attributes_dict.clear()
	for _name in attributes_runtime_dict:
		var runtime_attribute       = attributes_runtime_dict[_name]
		var derived_attribute_names = runtime_attribute.derived_from()

		for derived_name in derived_attribute_names:
			var derived_attribute: Attribute = find_attribute(derived_name)
			if not is_instance_valid(derived_attribute):
				push_warning("_create_derived_attributes failed | 未找到该名称的依赖属性 %s" % derived_name)
				continue

			if not derived_attributes_dict.has(derived_attribute.attribute_name):
				derived_attributes_dict[derived_attribute.attribute_name] = []
			var relative_attributes = derived_attributes_dict[derived_attribute.attribute_name]
			relative_attributes.append(runtime_attribute)


func _init_runtime_attributes():
	var in_degree: Dictionary                       = {}
	var graph: Dictionary                           = {} # Stores outbound edges: key is depended on by values in array
	var nodes_without_incoming_edge: Array[Variant] = []

	# Build graph and in_degree map
	for name in attributes_runtime_dict:
		in_degree[name] = 0
		graph[name] = []

	for name in attributes_runtime_dict:
		var runtime_attribute = attributes_runtime_dict[name]
		var dependencies      = runtime_attribute.derived_from()
		in_degree[name] = dependencies.size()

		for dep_name in dependencies:
			if not graph.has(dep_name):
				push_warning("Dependency '%s' for attribute '%s' not found in the same AttributeSet." % [dep_name, name])
				continue
			graph[dep_name].append(name)

	# Find nodes with in-degree of 0
	for name in in_degree:
		if in_degree[name] == 0:
			nodes_without_incoming_edge.append(name)

	# Process nodes in topological order
	var initialized_attributes: Array[Variant] = []
	while not nodes_without_incoming_edge.is_empty():
		var attr_name = nodes_without_incoming_edge.pop_front()
		initialized_attributes.append(attr_name)

		var runtime_attribute = attributes_runtime_dict[attr_name]
		var base_value        = runtime_attribute.get_base_value()
		# Lazy-compute：初始化时仅写入数值，不触发公式
		runtime_attribute.computed_value = base_value

		for dependent_attr_name in graph[attr_name]:
			in_degree[dependent_attr_name] -= 1
			if in_degree[dependent_attr_name] == 0:
				nodes_without_incoming_edge.append(dependent_attr_name)

	# Check for cycles
	if initialized_attributes.size() != attributes_runtime_dict.size():
		push_error("A cycle was detected in the attribute dependency graph. Some attributes could not be initialized.")


func _on_attribute_changed(attribute: Attribute) -> void:
	if is_initializing:
		return
	_update_derived_attributes(attribute)


func _update_derived_attributes(derived_attribute: Attribute):
	if derived_attributes_dict.has(derived_attribute.attribute_name):
		var relative_attributes = derived_attributes_dict[derived_attribute.attribute_name]
		for attribute in relative_attributes:
			attribute.update_computed_value()
			#endregion
