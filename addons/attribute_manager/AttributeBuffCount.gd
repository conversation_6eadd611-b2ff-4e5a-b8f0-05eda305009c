@tool
class_name AttributeBuffCount extends AttributeBuff

## 最大生效次数
var max_counts: int:
	get:
		return attribute_set.find_attribute("max_counts").get_value()
	set(v):
		attribute_set.find_attribute("max_counts").set_value(v)

## 当前剩余次数
var remaining_counts: int

func _init():
	super._init()
	if not attribute_set.has_attribute("max_counts"):
		attribute_set.add_attribute("max_counts", Attribute.create(1))


static func create(_operation := AttributeModifier.OperationType.ADD, _value: float = 0.0, _name := "", _max_counts := 1) -> AttributeBuffCount:
	var buff = AttributeBuffCount.new()
	var attributes: Dictionary[StringName, Attribute] = {}
	attributes["value"] = Attribute.create(_value)
	attributes["duration"] = Attribute.create(0.0)
	attributes["max_stacks"] = Attribute.create(1)
	attributes["max_counts"] = Attribute.create(_max_counts)
	buff.attribute_set.attributes = attributes
	buff.operation = _operation
	buff.buff_name = _name
	buff.remaining_counts = _max_counts
	buff.attribute_modifier = AttributeModifier.new(_operation, _value)
	return buff


func consume() -> bool:
	"""
	消耗一次使用次数。如果次数耗尽，则标记为待移除。
	返回: true 如果次数被成功消耗, false 如果已经耗尽。
	"""
	if is_pending_remove or remaining_counts <= 0:
		return false

	remaining_counts -= 1
	if remaining_counts <= 0:
		is_pending_remove = true

	return true


func duplicate_buff() -> AttributeBuff:
	var duplicated := super.duplicate_buff() as AttributeBuffCount
	if duplicated:
		duplicated.max_counts = self.max_counts
		duplicated.remaining_counts = self.max_counts
	return duplicated
