@tool
class_name AttributeBuff extends Resource
@export var buff_name: String
## 操作类型
@export var operation := AttributeModifier.OperationType.ADD
## 操作值
var value: float:
	get:
		return attribute_set.find_attribute("value").get_value()
	set(v):
		attribute_set.find_attribute("value").set_value(v)

## 持续时间策略
@export var policy := DurationPolicy.Infinite
## 持续时间
## duration_policy == HasDuration生效
## 单位：秒
var duration: float:
	get:
		return attribute_set.find_attribute("duration").get_value()
	set(v):
		attribute_set.find_attribute("duration").set_value(v)

## 持续时间合并策略
@export var merging := DurationMerging.Restart
## 最大叠加层数 0为无限, 1为不叠加
var max_stacks : int:
	get:
		return attribute_set.find_attribute("max_stacks").get_value()
	set(v):
		attribute_set.find_attribute("max_stacks").set_value(v)

@export var attribute_set: AttributeSet

func _init():
	if attribute_set == null:
		attribute_set = AttributeSet.new()
	if not attribute_set.has_attribute("value"):
		attribute_set.add_attribute("value", Attribute.create(0.0))
	if not attribute_set.has_attribute("duration"):
		attribute_set.add_attribute("duration", Attribute.create(0.0))
	if not attribute_set.has_attribute("max_stacks"):
		attribute_set.add_attribute("max_stacks", Attribute.create(1))


static func create(_operation := AttributeModifier.OperationType.ADD, _value: float = 0.0, _name := "")->AttributeBuff:
	var buff = AttributeBuff.new()
	var attributes: Dictionary[StringName, Attribute] = {}
	attributes["value"] = Attribute.create(_value)
	attributes["duration"] = Attribute.create(0.0)
	attributes["max_stacks"] = Attribute.create(1)
	buff.attribute_set.attributes = attributes
	buff.operation = _operation
	buff.buff_name = _name
	buff.attribute_modifier = AttributeModifier.new(_operation, _value)
	return buff


## 当前叠加层数
var current_stacks: int = 1:
	set(v):
		current_stacks = v
		if is_instance_valid(attribute_modifier):
			attribute_modifier.value = stack_value
## 叠加后的值
var stack_value:
	get:
		return value * current_stacks

enum DurationPolicy {
	Infinite,		## 持久地·
	HasDuration,	## 有时效性地
}

enum DurationMerging {
	Restart,	## 重新开始计算时长
	Addtion,	## 新的时长叠加到现有时效上
	NoEffect,	## 对现有时效无任何影响
}

var attribute_modifier: AttributeModifier
var remaining_time: float
var is_pending_remove := false
var applied_attribute:
	get():
		return applied_attribute.get_ref() if is_instance_valid(applied_attribute) else null


func duplicate_buff() -> AttributeBuff:
	var duplicated_buff = duplicate(true)
	duplicated_buff.attribute_modifier = AttributeModifier.new(operation, value)
	duplicated_buff.set_duration(duration)
	duplicated_buff.max_stacks = max_stacks
	duplicated_buff.current_stacks = current_stacks

	return duplicated_buff


## 由应用目标属性驱动
func run_process(delta: float):
	if has_duration() and not is_pending_remove:
		remaining_time = max(remaining_time - delta, 0.0)
		if is_zero_approx(remaining_time):
			is_pending_remove = true


static func add(_value: float = 0.0, _name := "") -> AttributeBuff:
	return AttributeBuff.create(AttributeModifier.OperationType.ADD, _value, _name)


static func sub(_value: float = 0.0, _name := "") -> AttributeBuff:
	return AttributeBuff.create(AttributeModifier.OperationType.SUB, _value, _name)


static func mult(_value: float = 0.0, _name := "") -> AttributeBuff:
	return AttributeBuff.create(AttributeModifier.OperationType.MULT, _value, _name)


static func div(_value: float = 0.0, _name := "") -> AttributeBuff:
	return AttributeBuff.create(AttributeModifier.OperationType.DIVIDE, _value, _name)


func operate(base_value: float) -> float:
	attribute_modifier.value = stack_value
	return attribute_modifier.operate(base_value)


func has_duration() -> bool:
	return policy == DurationPolicy.HasDuration


func set_merging(_mergin: DurationMerging):
	merging = _mergin


func set_duration(_time: float) -> AttributeBuff:
	duration = _time
	remaining_time = duration
	if duration > 0.0:
		policy = DurationPolicy.HasDuration
	return self


func restart_duration():
	remaining_time = duration


func extend_duration(_time: float):
	remaining_time += _time

func get_attribute_value(attribute_name: String) -> float:
	return attribute_set.find_attribute(attribute_name).get_value()


func set_attribute_value(attribute_name: String, value: float):
	attribute_set.find_attribute(attribute_name).set_value(value)
