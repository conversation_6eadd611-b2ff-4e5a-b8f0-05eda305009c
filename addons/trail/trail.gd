extends Line2D
class_name Trail

# 拖尾最大长度
@export var max_points: int = 30
@export var use_offset: bool = false

var previous_position: Vector2 = Vector2.ZERO
var offset: Vector2 = Vector2.ZERO

func _physics_process(delta: float) -> void:
	var current_position: Vector2 = get_parent().global_position
	
	if current_position == previous_position:
		return

	var dir = (current_position - previous_position).normalized()
	var radius = get_node_size().x * 0.5 if use_offset else 0
	
	add_point(current_position - dir * radius)
	if get_point_count() > max_points:
		remove_point(0)
	previous_position = current_position


func get_node_size() -> Vector2:
	# 优先判断自身是否为 Sprite2D
	if get_class() == "Sprite2D":
		if self.texture:
			return self.texture.get_size() * self.scale
	if get_parent().get_class() == "Sprite2D":
		if get_parent().texture:
			return get_parent().texture.get_size() * get_parent().scale
	# 再判断是否有 Sprite2D 子节点
	if has_node("Sprite2D"):
		var sprite = $Sprite2D
		if sprite.texture:
			return sprite.texture.get_size() * sprite.scale
	# 其他情况尝试 get_rect
	elif has_method("get_rect"):
		return call("get_rect").size
	return Vector2(100, 100) # 默认尺寸
