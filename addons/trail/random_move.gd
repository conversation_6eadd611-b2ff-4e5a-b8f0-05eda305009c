extends Node2D

@export var speed: float = 200.0 # 速度，像素/秒
@export var direction_change_interval: float = 1.5 # 方向改变间隔，秒
var velocity: Vector2 = Vector2.ZERO
var time_since_direction_change := 0.0

func _ready():
    randomize()
    _set_random_direction()

func _process(delta):
    time_since_direction_change += delta
    if time_since_direction_change >= direction_change_interval:
        _set_random_direction()
        time_since_direction_change = 0.0
    
    var new_position = position + velocity * delta
    var screen_rect = get_viewport_rect()
    var half_size = get_node_size() * 0.5
    # 边界检测与反弹
    if new_position.x - half_size.x < 0:
        new_position.x = half_size.x
        velocity.x = abs(velocity.x)
    elif new_position.x + half_size.x > screen_rect.size.x:
        new_position.x = screen_rect.size.x - half_size.x
        velocity.x = -abs(velocity.x)
    if new_position.y - half_size.y < 0:
        new_position.y = half_size.y
        velocity.y = abs(velocity.y)
    elif new_position.y + half_size.y > screen_rect.size.y:
        new_position.y = screen_rect.size.y - half_size.y
        velocity.y = -abs(velocity.y)
    position = new_position

func _set_random_direction():
    var angle = randf() * TAU
    velocity = Vector2.RIGHT.rotated(angle) * speed

func get_node_size() -> Vector2:
    # 优先判断自身是否为 Sprite2D
    if get_class() == "Sprite2D":
        if self.texture:
            return self.texture.get_size() * self.scale
    # 再判断是否有 Sprite2D 子节点
    if has_node("Sprite2D"):
        var sprite = $Sprite2D
        if sprite.texture:
            return sprite.texture.get_size() * sprite.scale
    # 其他情况尝试 get_rect
    elif has_method("get_rect"):
        return call("get_rect").size
    return Vector2(100, 100) # 默认尺寸

