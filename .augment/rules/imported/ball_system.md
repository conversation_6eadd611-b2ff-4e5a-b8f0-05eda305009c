---
type: "agent_requested"
---

---
# 弹球系统实现原理

## 整体架构
- 基于RigidBody2D实现的物理弹球系统，带有组件化能力设计
- 通过BallPoolManager进行弹球的创建、管理和回收
- 弹球具有生命周期：初始化、活跃、冷却、加速下落、回收

## 物理交互机制
- 使用物理材质控制弹跳和摩擦特性
- 重写_integrate_forces实现恒速移动和受控加速
- 通过contact_monitor监听碰撞事件，触发能力效果
- 支持重力系统切换，实现冷却超时后的加速下落

## 能力系统
- 弹球拥有Abilities节点容器，可挂载多个能力组件
- 所有能力继承自BallAbilityBase基类，提供统一接口
- 能力系统提供四个关键钩子函数:
  - on_ball_collision: 碰撞时触发
  - on_physics_update: 每帧更新
  - on_primary_hit: 主目标命中
  - on_secondary_hit: 次要目标命中（连锁效应）

## 视觉效果
- 使用ShaderMaterial实现动态材质效果，如火焰、闪电等
- 集成拖尾系统(Trail)实现移动轨迹可视化
- 通过GPUParticles2D添加粒子效果增强视觉表现
- 支持不同弹球类型的独特视觉风格

## Buff应用案例
- 以FireAbility为例，命中敌人时应用燃烧Buff
- 燃烧效果通过AttributeBuffDOT实现，可配置伤害、持续时间和触发频率
- 使用BurnEffectHandler处理视觉反馈，包括燃烧材质和死亡特效
- 支持随等级成长的伤害值，通过LeveledAttribute实现

## 扩展性设计
- 新增弹球类型只需创建预制体并配置相应能力和视觉效果
- 能力之间可相互组合，且支持级联触发效果
- 通过GameEvents信号系统允许外部系统（如遗物）响应弹球事件
- 使用属性系统支持弹球数值的灵活配置和实时调整


