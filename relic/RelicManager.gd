class_name RelicManager extends Node

var relics: Array[Relic] = []


func add_relic(relic_resource: Relic):
	if not relic_resource in relics:
		relics.append(relic_resource)
		if is_instance_valid(relic_resource.effect):
			relic_resource.effect.on_acquired(get_parent())


func remove_relic(relic_resource: Relic):
	if relic_resource in relics:
		relics.erase(relic_resource)
		if is_instance_valid(relic_resource.effect):
			relic_resource.effect.on_lost(get_parent()) 