class_name PosConditionRelicEffect extends RelicEffect

## 要应用的次数Buff资源 (增幅 0.2 = 20%)
@export var buff_to_apply: AttributeBuffCount
## 弹球上用来存储临时暴击率加成的属性名
@export var apply_attribute: String = "crit"
## 判断“正下方” 的阈值，单位像素。ball.y - enemy.y > threshold 视为从下方命中
@export var distance_threshold: float = 4.0
enum PosCondition {
	DOWN,
	UP,
	LEFT,
	RIGHT,
}
## 触发条件
@export var pos_condition: PosCondition = PosCondition.DOWN

func on_acquired(_target: Node):
	GameEvents.ball_hit_enemy.connect(_on_ball_hit_enemy)

func on_lost(_target: Node):
	if GameEvents.is_connected("ball_hit_enemy", Callable(self, "_on_ball_hit_enemy")):
		GameEvents.ball_hit_enemy.disconnect(_on_ball_hit_enemy)

func _on_ball_hit_enemy(ball: Node, enemy: Node, _collision_info: Dictionary):
	# 只有当弹球位于敌人下方时才触发
	match pos_condition:
		PosCondition.DOWN:
			if ball.global_position.y <= enemy.global_position.y + distance_threshold:
				return
		PosCondition.UP:
			if ball.global_position.y >= enemy.global_position.y - distance_threshold:
				return
		PosCondition.LEFT:
			if ball.global_position.x <= enemy.global_position.x - distance_threshold:
				return
		PosCondition.RIGHT:
			if ball.global_position.x >= enemy.global_position.x + distance_threshold:
				return
	
	var ball_attr_comp := ball.get_node_or_null("AttributeComponent") as AttributeComponent
	if not is_instance_valid(ball_attr_comp):
		return
	
	if not ball_attr_comp.attribute_set.has_attribute(apply_attribute):
		return
	
	var attr = ball_attr_comp.attribute_set.find_attribute(apply_attribute)
	attr.add_buff(buff_to_apply) 