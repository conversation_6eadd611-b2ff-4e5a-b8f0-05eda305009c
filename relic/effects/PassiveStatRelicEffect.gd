class_name PassiveStatRelicEffect extends RelicEffect

## 要修改的属性名称
@export var attribute_name: StringName
## 要应用的Buff资源
@export var buff: AttributeBuff

## 存储已应用的Buff实例，以便在失去遗物时能够正确移除
var _applied_buff_instance: AttributeBuff


func on_acquired(target: Node):
	var attribute_comp = target.get_node("AttributeComponent") as AttributeComponent
	if not is_instance_valid(attribute_comp):
		push_error("Relic target '%s' does not have an AttributeComponent." % target.name)
		return
		
	if attribute_comp.has_attribute(String(attribute_name)):
		var attribute = attribute_comp.find_attribute(String(attribute_name))
		_applied_buff_instance = attribute.add_buff(buff)
	else:
		push_warning("Attribute '%s' not found on target '%s' for relic effect." % [attribute_name, target.name])


func on_lost(target: Node):
	if not is_instance_valid(_applied_buff_instance):
		return # 没有已应用的Buff实例，无需操作

	var attribute_comp = target.get_node("AttributeComponent")
	if not is_instance_valid(attribute_comp):
		return # 目标已失效或没有组件，无法移除
		
	if attribute_comp.attribute_set.has_attribute(String(attribute_name)):
		var attribute = attribute_comp.attribute_set.find_attribute(String(attribute_name))
		attribute.remove_buff(_applied_buff_instance) 