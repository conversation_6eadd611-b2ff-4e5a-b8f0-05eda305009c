class_name BuffHandlerBase
extends Resource

## 这个处理器能处理的buff名称 (e.g., "burn", "frost")
@export var handled_buff_name: String = ""

## 当buff被添加时调用
func on_buff_added(target: Node, buff: AttributeBuff):
	pass

## 当buff被移除时调用
func on_buff_removed(target: Node, buff: AttributeBuff):
	pass

## 返回true则由handler处理死亡，否则由enemy_base处理
func handle_enemy_death_effect(target: EnemyBase) -> bool:
	return false

## 当DOT触发时调用 (可选)
func on_dot_triggered(attribute: Attribute, buff: AttributeBuff, target: Node):
	pass

## 当buff的持续时间刷新时调用 (可选)
func on_duration_restarted(target: Node, buff: AttributeBuff):
	pass 