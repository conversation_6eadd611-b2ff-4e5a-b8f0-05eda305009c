[gd_scene load_steps=82 format=3 uid="uid://cqjbmotilkpv8"]

[ext_resource type="PackedScene" uid="uid://dbmyoo1q7b76k" path="res://enemy/prefab/box_shape.tscn" id="1_d5dsx"]
[ext_resource type="Shader" uid="uid://b1bbkbalcemjd" path="res://assets/materials/shader/texture_shader.gdshader" id="2_6g3h4"]
[ext_resource type="PackedScene" uid="uid://b5yhb4ta5ddfx" path="res://enemy/prefab/level1/level_1_enemy_1.tscn" id="2_06v32"]
[ext_resource type="Texture2D" uid="uid://npe7tklubw7k" path="res://assets/imgs/texture/T_2_2_Wood_basecolor.png" id="3_f1ywe"]
[ext_resource type="Script" uid="uid://bitevrrtbjqxc" path="res://enemy/enemy_vis.gd" id="5_abcde"]
[ext_resource type="Texture2D" uid="uid://busyyekx0fik" path="res://assets/imgs/enemys/s/attack.png" id="6_ibsu5"]
[ext_resource type="Texture2D" uid="uid://dcslbesulxinl" path="res://assets/imgs/enemys/s/idle.png" id="7_102re"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_jk6eq"]
frequency = 0.0065
offset = Vector3(-538.46, 0, 0)
fractal_lacunarity = 3.73
fractal_gain = 0.27

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_06v32"]
seamless = true
seamless_blend_skirt = 1.0
noise = SubResource("FastNoiseLite_jk6eq")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_aeque"]
shader = ExtResource("2_6g3h4")
shader_parameter/gradient_color = Color(0, 0, 0, 0.764706)
shader_parameter/gradient_solid_ratio = 0.0
shader_parameter/gradient_transition_ratio = 1.0
shader_parameter/noise_texture = SubResource("NoiseTexture2D_06v32")
shader_parameter/dirt_strength = 0.637
shader_parameter/dirt_scale = Vector2(0.825, 0.72)
shader_parameter/dirt_color = Color(0.165528, 0.122197, 0.0788577, 1)
shader_parameter/dirt_threshold = 0.342
shader_parameter/use_anti_tile = true

[sub_resource type="AtlasTexture" id="AtlasTexture_3pm82"]
atlas = ExtResource("6_ibsu5")
region = Rect2(0, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_uhced"]
atlas = ExtResource("6_ibsu5")
region = Rect2(45, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_46ug6"]
atlas = ExtResource("6_ibsu5")
region = Rect2(90, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_qnu8c"]
atlas = ExtResource("6_ibsu5")
region = Rect2(135, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_pkssk"]
atlas = ExtResource("6_ibsu5")
region = Rect2(180, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_lmqfq"]
atlas = ExtResource("6_ibsu5")
region = Rect2(225, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_obtsn"]
atlas = ExtResource("6_ibsu5")
region = Rect2(270, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_wrdls"]
atlas = ExtResource("6_ibsu5")
region = Rect2(315, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_lqc4t"]
atlas = ExtResource("6_ibsu5")
region = Rect2(360, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_raic6"]
atlas = ExtResource("6_ibsu5")
region = Rect2(405, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_bve1a"]
atlas = ExtResource("6_ibsu5")
region = Rect2(450, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_lja17"]
atlas = ExtResource("6_ibsu5")
region = Rect2(495, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_ygd1q"]
atlas = ExtResource("6_ibsu5")
region = Rect2(540, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_ce65l"]
atlas = ExtResource("6_ibsu5")
region = Rect2(585, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_mfc0n"]
atlas = ExtResource("6_ibsu5")
region = Rect2(630, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_5jmjk"]
atlas = ExtResource("7_102re")
region = Rect2(0, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ibr3i"]
atlas = ExtResource("7_102re")
region = Rect2(44, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_g4ph8"]
atlas = ExtResource("7_102re")
region = Rect2(88, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_l43o6"]
atlas = ExtResource("7_102re")
region = Rect2(132, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_wss4i"]
atlas = ExtResource("7_102re")
region = Rect2(176, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_a6612"]
atlas = ExtResource("7_102re")
region = Rect2(220, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ruwe3"]
atlas = ExtResource("7_102re")
region = Rect2(264, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_20jb7"]
atlas = ExtResource("7_102re")
region = Rect2(308, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qfssy"]
atlas = ExtResource("7_102re")
region = Rect2(352, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ult5q"]
atlas = ExtResource("7_102re")
region = Rect2(396, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_62i35"]
atlas = ExtResource("7_102re")
region = Rect2(440, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_8qoac"]
atlas = ExtResource("7_102re")
region = Rect2(484, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_7di47"]
atlas = ExtResource("7_102re")
region = Rect2(528, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_1brgh"]
atlas = ExtResource("7_102re")
region = Rect2(572, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_8i3ua"]
atlas = ExtResource("7_102re")
region = Rect2(616, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qbd75"]
atlas = ExtResource("7_102re")
region = Rect2(660, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_t88w8"]
atlas = ExtResource("7_102re")
region = Rect2(704, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_xdwr7"]
atlas = ExtResource("7_102re")
region = Rect2(748, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_78rtg"]
atlas = ExtResource("7_102re")
region = Rect2(792, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_idw37"]
atlas = ExtResource("7_102re")
region = Rect2(836, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_c155j"]
atlas = ExtResource("7_102re")
region = Rect2(880, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_01ss5"]
atlas = ExtResource("7_102re")
region = Rect2(924, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_1yjqn"]
atlas = ExtResource("7_102re")
region = Rect2(968, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_3b3cn"]
atlas = ExtResource("7_102re")
region = Rect2(1012, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_5l18f"]
atlas = ExtResource("7_102re")
region = Rect2(1056, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_22210"]
atlas = ExtResource("7_102re")
region = Rect2(1100, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_4d8y3"]
atlas = ExtResource("7_102re")
region = Rect2(1144, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_kquw4"]
atlas = ExtResource("7_102re")
region = Rect2(1188, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_sw2is"]
atlas = ExtResource("7_102re")
region = Rect2(1232, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_whm2e"]
atlas = ExtResource("7_102re")
region = Rect2(1276, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_oa4ue"]
atlas = ExtResource("7_102re")
region = Rect2(1320, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_gfcds"]
atlas = ExtResource("7_102re")
region = Rect2(1364, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_lbpoi"]
atlas = ExtResource("7_102re")
region = Rect2(1408, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ps5gq"]
atlas = ExtResource("7_102re")
region = Rect2(1452, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_o56ka"]
atlas = ExtResource("7_102re")
region = Rect2(1496, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_h7v3t"]
atlas = ExtResource("7_102re")
region = Rect2(1540, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qn6j5"]
atlas = ExtResource("7_102re")
region = Rect2(1584, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qlli3"]
atlas = ExtResource("7_102re")
region = Rect2(1628, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_tfbfv"]
atlas = ExtResource("7_102re")
region = Rect2(1672, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_v43ut"]
atlas = ExtResource("7_102re")
region = Rect2(1716, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qdent"]
atlas = ExtResource("7_102re")
region = Rect2(1760, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_13m8y"]
atlas = ExtResource("7_102re")
region = Rect2(1804, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_7486c"]
atlas = ExtResource("7_102re")
region = Rect2(1848, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_kljbi"]
atlas = ExtResource("7_102re")
region = Rect2(1892, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_80fac"]
atlas = ExtResource("7_102re")
region = Rect2(1936, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_6l0b8"]
atlas = ExtResource("7_102re")
region = Rect2(1980, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_se3dh"]
atlas = ExtResource("7_102re")
region = Rect2(2024, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_jfnr6"]
atlas = ExtResource("7_102re")
region = Rect2(2068, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ebue"]
atlas = ExtResource("7_102re")
region = Rect2(2112, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_81xqh"]
atlas = ExtResource("7_102re")
region = Rect2(2156, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_4rlc1"]
atlas = ExtResource("7_102re")
region = Rect2(2200, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qimke"]
atlas = ExtResource("7_102re")
region = Rect2(2244, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_5qeqy"]
atlas = ExtResource("7_102re")
region = Rect2(2288, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_caj1v"]
atlas = ExtResource("7_102re")
region = Rect2(2332, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_hw7an"]
atlas = ExtResource("7_102re")
region = Rect2(2376, 0, 44, 41)

[sub_resource type="SpriteFrames" id="SpriteFrames_elh7l"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3pm82")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uhced")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_46ug6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qnu8c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pkssk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lmqfq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_obtsn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wrdls")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lqc4t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_raic6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bve1a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lja17")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ygd1q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ce65l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mfc0n")
}],
"loop": false,
"name": &"attack",
"speed": 24.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_5jmjk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ibr3i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g4ph8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l43o6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wss4i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a6612")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ruwe3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_20jb7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qfssy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ult5q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_62i35")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8qoac")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7di47")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1brgh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8i3ua")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qbd75")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t88w8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xdwr7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_78rtg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_idw37")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c155j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_01ss5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1yjqn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3b3cn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5l18f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_22210")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4d8y3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kquw4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sw2is")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_whm2e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oa4ue")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gfcds")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lbpoi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ps5gq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o56ka")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h7v3t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qn6j5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qlli3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tfbfv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v43ut")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qdent")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_13m8y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7486c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kljbi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_80fac")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6l0b8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_se3dh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jfnr6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ebue")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_81xqh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4rlc1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qimke")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5qeqy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_caj1v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hw7an")
}],
"loop": true,
"name": &"idle",
"speed": 24.0
}]

[node name="EnemyVis" type="Node2D"]
script = ExtResource("5_abcde")
enemy_unit_scenes = Array[PackedScene]([ExtResource("2_06v32")])
monster_scale_factor = 0.8

[node name="BoxShape" parent="." instance=ExtResource("1_d5dsx")]
material = SubResource("ShaderMaterial_aeque")
instance_shader_parameters/dirt_uv_offset = Vector2(0.587854, -0.719152)
instance_shader_parameters/texture_uv_offset = Vector2(0.587854, -0.719152)
row_count = 2
row_1 = 1
row_3 = 0
cell_size = Vector2(80, 60)
thickness = 10.0
outline_thickness = 0.0
top_outline_thickness = 1.5
depth = 20.0
top_color = Color(0.745098, 0.745098, 0.745098, 0)
side_color = Color(1, 1, 1, 0.270588)
top_outline_color = Color(0, 0, 0, 1)
top_texture = ExtResource("3_f1ywe")
side_texture = ExtResource("3_f1ywe")
front_texture = ExtResource("3_f1ywe")
use_texture = true
top_texture_scale = Vector2(0.225, 0.12)
side_texture_scale = Vector2(3.535, 0.12)
front_texture_scale = Vector2(1.55, 1.37)
perspective_top = 0.5
perspective_bottom = 1.2

[node name="MaskClipper" type="Control" parent="."]
clip_children = 1
clip_contents = true
layout_mode = 3
anchors_preset = 0
offset_left = -140.892
offset_top = -140.0
offset_right = 142.559
offset_bottom = 20.0

[node name="Level1Enemy1" parent="MaskClipper" instance=ExtResource("2_06v32")]
position = Vector2(220.892, 80)
scale = Vector2(1.45455, 1.45455)
sprite_frames = SubResource("SpriteFrames_elh7l")
frame = 3

[node name="@AnimatedSprite2D@75466" parent="MaskClipper" instance=ExtResource("2_06v32")]
position = Vector2(60.892, 140)
scale = Vector2(1.45455, 1.45455)
sprite_frames = SubResource("SpriteFrames_elh7l")
frame = 12

[node name="@AnimatedSprite2D@75467" parent="MaskClipper" instance=ExtResource("2_06v32")]
position = Vector2(140.892, 140)
scale = Vector2(1.45455, 1.45455)
sprite_frames = SubResource("SpriteFrames_elh7l")
frame = 14

[node name="@AnimatedSprite2D@75468" parent="MaskClipper" instance=ExtResource("2_06v32")]
position = Vector2(220.892, 140)
scale = Vector2(1.45455, 1.45455)
sprite_frames = SubResource("SpriteFrames_elh7l")
frame = 32
