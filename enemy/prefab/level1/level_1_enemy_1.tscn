[gd_scene load_steps=84 format=3 uid="uid://b5yhb4ta5ddfx"]

[ext_resource type="Texture2D" uid="uid://busyyekx0fik" path="res://assets/imgs/enemys/s/attack.png" id="1_eo7hk"]
[ext_resource type="Script" uid="uid://dsryq1q032ul0" path="res://enemy/enemy_unit.gd" id="1_unit_script"]
[ext_resource type="Texture2D" uid="uid://dcslbesulxinl" path="res://assets/imgs/enemys/s/idle.png" id="2_vglex"]
[ext_resource type="PackedScene" uid="uid://bw8xk7y2n8qfj" path="res://enemy/prefab/bullet/fire_bullet.tscn" id="4_di875"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="4_f21pw"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="5_di875"]
[ext_resource type="Script" uid="uid://ucf5nur2irtk" path="res://attribute/scored_attribute.gd" id="6_n3s7x"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="7_hhlcv"]

[sub_resource type="AtlasTexture" id="AtlasTexture_3pm82"]
atlas = ExtResource("1_eo7hk")
region = Rect2(0, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_uhced"]
atlas = ExtResource("1_eo7hk")
region = Rect2(45, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_46ug6"]
atlas = ExtResource("1_eo7hk")
region = Rect2(90, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_qnu8c"]
atlas = ExtResource("1_eo7hk")
region = Rect2(135, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_pkssk"]
atlas = ExtResource("1_eo7hk")
region = Rect2(180, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_lmqfq"]
atlas = ExtResource("1_eo7hk")
region = Rect2(225, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_obtsn"]
atlas = ExtResource("1_eo7hk")
region = Rect2(270, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_wrdls"]
atlas = ExtResource("1_eo7hk")
region = Rect2(315, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_lqc4t"]
atlas = ExtResource("1_eo7hk")
region = Rect2(360, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_raic6"]
atlas = ExtResource("1_eo7hk")
region = Rect2(405, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_bve1a"]
atlas = ExtResource("1_eo7hk")
region = Rect2(450, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_lja17"]
atlas = ExtResource("1_eo7hk")
region = Rect2(495, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_ygd1q"]
atlas = ExtResource("1_eo7hk")
region = Rect2(540, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_ce65l"]
atlas = ExtResource("1_eo7hk")
region = Rect2(585, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_mfc0n"]
atlas = ExtResource("1_eo7hk")
region = Rect2(630, 0, 45, 48)

[sub_resource type="AtlasTexture" id="AtlasTexture_5jmjk"]
atlas = ExtResource("2_vglex")
region = Rect2(0, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ibr3i"]
atlas = ExtResource("2_vglex")
region = Rect2(44, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_g4ph8"]
atlas = ExtResource("2_vglex")
region = Rect2(88, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_l43o6"]
atlas = ExtResource("2_vglex")
region = Rect2(132, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_wss4i"]
atlas = ExtResource("2_vglex")
region = Rect2(176, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_a6612"]
atlas = ExtResource("2_vglex")
region = Rect2(220, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ruwe3"]
atlas = ExtResource("2_vglex")
region = Rect2(264, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_20jb7"]
atlas = ExtResource("2_vglex")
region = Rect2(308, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qfssy"]
atlas = ExtResource("2_vglex")
region = Rect2(352, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ult5q"]
atlas = ExtResource("2_vglex")
region = Rect2(396, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_62i35"]
atlas = ExtResource("2_vglex")
region = Rect2(440, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_8qoac"]
atlas = ExtResource("2_vglex")
region = Rect2(484, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_7di47"]
atlas = ExtResource("2_vglex")
region = Rect2(528, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_1brgh"]
atlas = ExtResource("2_vglex")
region = Rect2(572, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_8i3ua"]
atlas = ExtResource("2_vglex")
region = Rect2(616, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qbd75"]
atlas = ExtResource("2_vglex")
region = Rect2(660, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_t88w8"]
atlas = ExtResource("2_vglex")
region = Rect2(704, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_xdwr7"]
atlas = ExtResource("2_vglex")
region = Rect2(748, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_78rtg"]
atlas = ExtResource("2_vglex")
region = Rect2(792, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_idw37"]
atlas = ExtResource("2_vglex")
region = Rect2(836, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_c155j"]
atlas = ExtResource("2_vglex")
region = Rect2(880, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_01ss5"]
atlas = ExtResource("2_vglex")
region = Rect2(924, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_1yjqn"]
atlas = ExtResource("2_vglex")
region = Rect2(968, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_3b3cn"]
atlas = ExtResource("2_vglex")
region = Rect2(1012, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_5l18f"]
atlas = ExtResource("2_vglex")
region = Rect2(1056, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_22210"]
atlas = ExtResource("2_vglex")
region = Rect2(1100, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_4d8y3"]
atlas = ExtResource("2_vglex")
region = Rect2(1144, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_kquw4"]
atlas = ExtResource("2_vglex")
region = Rect2(1188, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_sw2is"]
atlas = ExtResource("2_vglex")
region = Rect2(1232, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_whm2e"]
atlas = ExtResource("2_vglex")
region = Rect2(1276, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_oa4ue"]
atlas = ExtResource("2_vglex")
region = Rect2(1320, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_gfcds"]
atlas = ExtResource("2_vglex")
region = Rect2(1364, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_lbpoi"]
atlas = ExtResource("2_vglex")
region = Rect2(1408, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_ps5gq"]
atlas = ExtResource("2_vglex")
region = Rect2(1452, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_o56ka"]
atlas = ExtResource("2_vglex")
region = Rect2(1496, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_h7v3t"]
atlas = ExtResource("2_vglex")
region = Rect2(1540, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qn6j5"]
atlas = ExtResource("2_vglex")
region = Rect2(1584, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qlli3"]
atlas = ExtResource("2_vglex")
region = Rect2(1628, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_tfbfv"]
atlas = ExtResource("2_vglex")
region = Rect2(1672, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_v43ut"]
atlas = ExtResource("2_vglex")
region = Rect2(1716, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qdent"]
atlas = ExtResource("2_vglex")
region = Rect2(1760, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_13m8y"]
atlas = ExtResource("2_vglex")
region = Rect2(1804, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_7486c"]
atlas = ExtResource("2_vglex")
region = Rect2(1848, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_kljbi"]
atlas = ExtResource("2_vglex")
region = Rect2(1892, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_80fac"]
atlas = ExtResource("2_vglex")
region = Rect2(1936, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_6l0b8"]
atlas = ExtResource("2_vglex")
region = Rect2(1980, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_se3dh"]
atlas = ExtResource("2_vglex")
region = Rect2(2024, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_jfnr6"]
atlas = ExtResource("2_vglex")
region = Rect2(2068, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_5ebue"]
atlas = ExtResource("2_vglex")
region = Rect2(2112, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_81xqh"]
atlas = ExtResource("2_vglex")
region = Rect2(2156, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_4rlc1"]
atlas = ExtResource("2_vglex")
region = Rect2(2200, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_qimke"]
atlas = ExtResource("2_vglex")
region = Rect2(2244, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_5qeqy"]
atlas = ExtResource("2_vglex")
region = Rect2(2288, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_caj1v"]
atlas = ExtResource("2_vglex")
region = Rect2(2332, 0, 44, 41)

[sub_resource type="AtlasTexture" id="AtlasTexture_hw7an"]
atlas = ExtResource("2_vglex")
region = Rect2(2376, 0, 44, 41)

[sub_resource type="SpriteFrames" id="SpriteFrames_elh7l"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3pm82")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uhced")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_46ug6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qnu8c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pkssk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lmqfq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_obtsn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wrdls")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lqc4t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_raic6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bve1a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lja17")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ygd1q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ce65l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mfc0n")
}],
"loop": false,
"name": &"attack",
"speed": 24.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_5jmjk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ibr3i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g4ph8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l43o6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wss4i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a6612")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ruwe3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_20jb7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qfssy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ult5q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_62i35")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8qoac")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7di47")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1brgh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8i3ua")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qbd75")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t88w8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xdwr7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_78rtg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_idw37")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c155j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_01ss5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1yjqn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3b3cn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5l18f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_22210")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4d8y3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kquw4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sw2is")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_whm2e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oa4ue")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gfcds")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lbpoi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ps5gq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o56ka")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h7v3t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qn6j5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qlli3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tfbfv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v43ut")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qdent")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_13m8y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7486c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kljbi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_80fac")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6l0b8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_se3dh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jfnr6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5ebue")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_81xqh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4rlc1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qimke")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5qeqy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_caj1v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hw7an")
}],
"loop": true,
"name": &"idle",
"speed": 24.0
}]

[sub_resource type="Resource" id="Resource_6owt2"]
script = ExtResource("6_n3s7x")
growth_per_score = 0.001
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 10.0
can_cache = false
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_wdyd3"]
script = ExtResource("6_n3s7x")
growth_per_score = 0.001
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 5.0
can_cache = false
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_jitwm"]
script = ExtResource("5_di875")
base_value = 800.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_vfbbv"]
resource_local_to_scene = true
script = ExtResource("7_hhlcv")
attributes = Dictionary[StringName, ExtResource("5_di875")]({
&"attack_interval_max": SubResource("Resource_6owt2"),
&"attack_interval_min": SubResource("Resource_wdyd3"),
&"attack_range": SubResource("Resource_jitwm")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[node name="Level1Enemy1" type="AnimatedSprite2D"]
self_modulate = Color(0.507314, 0.507315, 0.507314, 1)
sprite_frames = SubResource("SpriteFrames_elh7l")
animation = &"idle"
autoplay = "idle"
script = ExtResource("1_unit_script")
can_attack = true
bullet_scene = ExtResource("4_di875")

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("4_f21pw")
attribute_set = SubResource("Resource_vfbbv")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"
