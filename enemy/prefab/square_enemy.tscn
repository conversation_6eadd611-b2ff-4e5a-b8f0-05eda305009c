[gd_scene load_steps=21 format=3 uid="uid://dd0s18e3gyj8l"]

[ext_resource type="Shader" uid="uid://brjwypq2onoc5" path="res://assets/materials/shader/hit_flash.tres" id="1_5s526"]
[ext_resource type="PhysicsMaterial" uid="uid://btxphd0oijm3l" path="res://assets/materials/physics/enemy.tres" id="1_f1u1f"]
[ext_resource type="Script" uid="uid://bppfk7e8ywv0n" path="res://enemy/enemy_base.gd" id="2_eepas"]
[ext_resource type="Texture2D" uid="uid://0kvtkg03psj8" path="res://assets/imgs/enemys/正方形.png" id="2_n2fix"]
[ext_resource type="Script" uid="uid://boyhjsm72rmvx" path="res://addons/shader_sprite/shaders_sprite_2d.gd" id="5_ko7l2"]
[ext_resource type="Shader" uid="uid://c7el4oom3yg8x" path="res://assets/materials/shader/stroke.gdshader" id="6_lxdl4"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="7_eepas"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="19_67d5e"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="19_dmbdr"]
[ext_resource type="Script" uid="uid://ucf5nur2irtk" path="res://attribute/scored_attribute.gd" id="20_dfdlw"]
[ext_resource type="Script" uid="uid://dvjh7sib4d3po" path="res://attribute/hp_attribute.gd" id="20_lm7jt"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_4e4bk"]
resource_local_to_scene = true
shader = ExtResource("1_5s526")
shader_parameter/hit_color = Color(1, 1, 1, 1)
shader_parameter/rate = 0.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_ljufv"]
resource_local_to_scene = true
shader = ExtResource("6_lxdl4")
shader_parameter/color = Color(0, 0, 0, 1)
shader_parameter/width = 3.0
shader_parameter/pattern = 1
shader_parameter/inside = false
shader_parameter/add_margins = true

[sub_resource type="RectangleShape2D" id="RectangleShape2D_vvn8l"]
size = Vector2(83.35, 86)

[sub_resource type="Resource" id="Resource_ljufv"]
script = ExtResource("20_dfdlw")
growth_per_score = 0.01
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_m807u"]
script = ExtResource("20_lm7jt")
base_value = 100.0
can_cache = true
metadata/_custom_type_script = "uid://dvjh7sib4d3po"

[sub_resource type="Resource" id="Resource_stq6y"]
script = ExtResource("20_dfdlw")
growth_per_score = 0.1
base_value = 100.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_dfdlw"]
script = ExtResource("20_dfdlw")
growth_per_score = 0.001
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_qpnmr"]
script = ExtResource("20_dfdlw")
growth_per_score = 0.001
base_value = 20.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_dmbdr"]
resource_local_to_scene = true
script = ExtResource("19_dmbdr")
attributes = Dictionary[StringName, ExtResource("19_67d5e")]({
&"damage": SubResource("Resource_ljufv"),
&"hp": SubResource("Resource_m807u"),
&"max_hp": SubResource("Resource_stq6y"),
&"score": SubResource("Resource_dfdlw"),
&"speed": SubResource("Resource_qpnmr")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[node name="SquareEnemy" type="RigidBody2D"]
collision_layer = 4
collision_mask = 7
mass = 1e+17
physics_material_override = ExtResource("1_f1u1f")
gravity_scale = 0.0
can_sleep = false
freeze_mode = 1
contact_monitor = true
max_contacts_reported = 5
linear_damp_mode = 1
angular_damp_mode = 1
angular_damp = 3.0
script = ExtResource("2_eepas")

[node name="Sprite2D" type="Sprite2D" parent="."]
material = SubResource("ShaderMaterial_4e4bk")
position = Vector2(3.8147e-06, 0)
scale = Vector2(0.8, 0.8)
script = ExtResource("5_ko7l2")
shaders_texture = ExtResource("2_n2fix")
shaders_dic = Dictionary[StringName, Material]({
&"hit_flash": SubResource("ShaderMaterial_4e4bk"),
&"stroke": SubResource("ShaderMaterial_ljufv")
})

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(0.325001, 0)
shape = SubResource("RectangleShape2D_vvn8l")

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("7_eepas")
attribute_set = SubResource("Resource_dmbdr")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
