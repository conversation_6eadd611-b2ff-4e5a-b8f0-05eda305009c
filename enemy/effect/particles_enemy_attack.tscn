[gd_scene load_steps=9 format=3 uid="uid://bin8fh7i3dhgf"]

[ext_resource type="Texture2D" uid="uid://c46f658slia78" path="res://assets/imgs/effects/34.png" id="1_w86ln"]
[ext_resource type="Script" uid="uid://dcx66e75435fp" path="res://effect/particle_root_effect.gd" id="2_7xi6a"]

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_7xi6a"]
blend_mode = 1

[sub_resource type="Curve" id="Curve_tudtq"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.483146, 0.797944), -0.877686, -0.877686, 0, 0, Vector2(1, 0), -2.34099, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_5pt8u"]
curve = SubResource("Curve_tudtq")

[sub_resource type="Curve" id="Curve_bwk58"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.0394548), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_q30rs"]
curve = SubResource("Curve_bwk58")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_uk3ff"]
lifetime_randomness = 0.5
particle_flag_disable_z = true
direction = Vector3(0, 0, 0)
spread = 180.0
initial_velocity_min = 150.0
initial_velocity_max = 200.0
gravity = Vector3(0, 0, 0)
radial_accel_min = -2.23517e-06
radial_accel_max = -2.23517e-06
damping_min = 100.0
damping_max = 300.0
scale_min = 0.08
scale_max = 0.12
scale_curve = SubResource("CurveTexture_q30rs")
color = Color(1.2, 0.2, 0.2, 1)
alpha_curve = SubResource("CurveTexture_5pt8u")

[node name="GPUParticlesEnemyAttack" type="GPUParticles2D"]
material = SubResource("CanvasItemMaterial_7xi6a")
emitting = false
amount = 30
texture = ExtResource("1_w86ln")
lifetime = 2.0
one_shot = true
explosiveness = 1.0
randomness = 1.0
process_material = SubResource("ParticleProcessMaterial_uk3ff")
script = ExtResource("2_7xi6a")
