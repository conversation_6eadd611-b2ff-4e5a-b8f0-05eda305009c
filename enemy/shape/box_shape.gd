@tool
extends Node2D
class_name BoxShape

@export_group("Shape Definition (3x3 Grid)")
@export_range(1, 2) var row_count: int = 2
@export_flags("Left:4", "Center:2", "Right:1") var row_1: int = 7:
	set(value):
		if row_1 == value: return
		row_1 = value
		_invalidate_geometry_cache()
		queue_redraw()

@export_flags("Left:4", "Center:2", "Right:1") var row_2: int = 7:
	set(value):
		if row_2 == value: return
		row_2 = value
		_invalidate_geometry_cache()
		queue_redraw()

@export_flags("Left:4", "Center:2", "Right:1") var row_3: int = 7:
	set(value):
		if row_3 == value: return
		row_3 = value
		_invalidate_geometry_cache()
		queue_redraw()

@export_group("Appearance")
@export var cell_size: Vector2 = Vector2(50, 50):
	set(value):
		if cell_size == value: return
		cell_size = value
		_invalidate_geometry_cache()
		queue_redraw()

@export var thickness: float = 10.0:
	set(value):
		if thickness == value: return
		thickness = value
		_invalidate_geometry_cache()
		queue_redraw()

@export var outline_thickness: float = 2.0:
	set(value):
		if outline_thickness == value: return
		outline_thickness = value
		_invalidate_geometry_cache()
		queue_redraw()

@export var top_outline_thickness: float = 0.0:
	set(value):
		if top_outline_thickness == value: return
		top_outline_thickness = value
		_invalidate_geometry_cache()
		queue_redraw()

@export var depth: float = 15.0:
	get:
		return depth
	set(value):
		if depth == value: return
		depth = value
		_invalidate_geometry_cache()
		queue_redraw()

@export_group("Colors")
@export var top_color: Color = Color.GRAY:
	set(value):
		if top_color == value: return
		top_color = value
		queue_redraw()

@export var side_color: Color = Color.DARK_GRAY:
	set(value):
		if side_color == value: return
		side_color = value
		queue_redraw()

@export var outline_color: Color = Color.BLACK:
	set(value):
		if outline_color == value: return
		outline_color = value
		queue_redraw()

@export var top_outline_color: Color = Color.WHITE:
	set(value):
		if top_outline_color == value: return
		top_outline_color = value
		queue_redraw()

@export var inner_color: Color = Color.BLACK:
	set(value):
		if inner_color == value: return
		inner_color = value
		queue_redraw()

@export_group("Texture")
@export var top_texture: Texture2D:
	set(value):
		if top_texture == value: return
		top_texture = value
		queue_redraw()

# 非顶面使用的纹理
@export var side_texture: Texture2D:
	set(value):
		if side_texture == value: return
		side_texture = value
		queue_redraw()

# 前正面使用的纹理
@export var front_texture: Texture2D:
	set(value):
		if front_texture == value: return
		front_texture = value
		queue_redraw()

# 内面使用的纹理
@export var inner_texture: Texture2D:
	set(value):
		if inner_texture == value: return
		inner_texture = value
		queue_redraw()

@export var use_texture: bool = false:
	set(value):
		if use_texture == value: return
		use_texture = value
		queue_redraw()

@export var top_texture_scale: Vector2 = Vector2(1.0, 1.0):
	set(value):
		if top_texture_scale == value: return
		top_texture_scale = value
		queue_redraw()

@export var side_texture_scale: Vector2 = Vector2(1.0, 1.0):
	set(value):
		if side_texture_scale == value: return
		side_texture_scale = value
		queue_redraw()

@export var inner_texture_scale: Vector2 = Vector2(1.0, 1.0):
	set(value):
		if inner_texture_scale == value: return
		inner_texture_scale = value
		queue_redraw()

# Bottom/front face 纹理缩放
@export var front_texture_scale: Vector2 = Vector2(1.0, 1.0):
	set(value):
		if front_texture_scale == value: return
		front_texture_scale = value
		queue_redraw()


@export_group("Perspective")
@export var perspective_total: float = 0.5:
	set(value):
		if perspective_total == value: return
		perspective_total = value
		_invalidate_geometry_cache()
		queue_redraw()

@export var perspective_top: float = 0.8:
	set(value):
		if perspective_top == value: return
		perspective_top = value
		_invalidate_geometry_cache()
		queue_redraw()

@export var perspective_bottom: float = 0.5:
	set(value):
		if perspective_bottom == value: return
		perspective_bottom = value
		_invalidate_geometry_cache()
		queue_redraw()

var _perspective_factor_override: float = -999.0

# 几何计算缓存
var _geometry_cache: Dictionary = {}
var _cache_valid: bool = false

# 使几何缓存失效
func _invalidate_geometry_cache():
	_cache_valid = false
	_geometry_cache.clear()

func set_perspective_factor(factor: float):
	if not is_equal_approx(_perspective_factor_override, factor):
		_perspective_factor_override = factor
		_invalidate_geometry_cache()
		queue_redraw()

func get_shape_rect() -> Rect2:
	var all_points = _get_all_transformed_vertices()

	if all_points.is_empty():
		return Rect2()

	var min_pos = all_points[0]
	var max_pos = all_points[0]

	for i in range(1, all_points.size()):
		min_pos.x = min(min_pos.x, all_points[i].x)
		min_pos.y = min(min_pos.y, all_points[i].y)
		max_pos.x = max(max_pos.x, all_points[i].x)
		max_pos.y = max(max_pos.y, all_points[i].y)

	var rect = Rect2(min_pos, max_pos - min_pos)
	# 使用 outline_thickness 作为边距，确保轮廓线也被包含在内
	return rect.grow(outline_thickness)


func _get_all_transformed_vertices() -> PackedVector2Array:
	var geo = _calculate_geometry()
	if geo.is_empty() or geo.front_polygon.is_empty():
		return PackedVector2Array()

	var all_vertices = PackedVector2Array()
	all_vertices.append_array(geo.front_polygon)
	all_vertices.append_array(geo.back_polygon)

	return all_vertices

func _calculate_geometry() -> Dictionary:
	# 检查缓存是否有效
	if _cache_valid and not _geometry_cache.is_empty():
		return _geometry_cache

	# 1. Calculate perspective_factor
	var perspective_factor = 0.0
	if _perspective_factor_override > -999.0:
		perspective_factor = _perspective_factor_override
	elif Engine.is_editor_hint():
		if get_viewport() == null: return {}
		var screen_rect = get_viewport().get_visible_rect()
		var h_offset = ((position.x - screen_rect.size.x / 2.0) / screen_rect.size.x) * perspective_total
		perspective_factor = clamp(h_offset, -0.8, 0.8)
	else:
		var screen_rect = get_viewport_rect()
		var h_offset = ((global_position.x - screen_rect.size.x / 2.0) / screen_rect.size.x) * perspective_total
		perspective_factor = clamp(h_offset, -0.8, 0.8)

	# 2. Calculate perspective_shift_x and polygons
	var perspective_shift_x = depth * perspective_factor
	var base_polygon = _get_base_polygon()
	if base_polygon.is_empty() or base_polygon.size() < 3:
		return {
			"front_polygon": PackedVector2Array(),
			"back_polygon": PackedVector2Array(),
			"inner_polygons": [],
			"perspective_factor": perspective_factor,
		}

	var front_polygon: PackedVector2Array = _apply_front_perspective(base_polygon, perspective_shift_x, perspective_factor)

	var back_polygon = PackedVector2Array()
	for p in front_polygon:
		back_polygon.append(p + Vector2(-perspective_shift_x * perspective_bottom, depth))
		
	var inner_polygons = Geometry2D.offset_polygon(front_polygon, -thickness)

	# 保存到缓存
	_geometry_cache = {
		"front_polygon": front_polygon,
		"back_polygon": back_polygon,
		"inner_polygons": inner_polygons,
		"perspective_factor": perspective_factor,
	}
	_cache_valid = true

	return _geometry_cache

# 获取前面多边形（用于遮罩计算）
func get_front_polygon() -> PackedVector2Array:
	var geo = _calculate_geometry()
	if geo.is_empty() or not geo.has("front_polygon"):
		return PackedVector2Array()
	return geo["front_polygon"]

# 获取用于绘制顶部遮罩的几何信息
func get_top_frame_geometry() -> Dictionary:
	var geo = _calculate_geometry()
	if geo.is_empty() or geo.front_polygon.is_empty():
		return {}

	# 返回绘制顶部边框所需的所有信息
	return {
		"front_polygon": geo.front_polygon,
		"inner_polygons": geo.inner_polygons,
		"outline_color": outline_color,
		"top_color": top_color,
		"inner_color": inner_color,
		"outline_thickness": outline_thickness
	}


# -- Drawing Logic -------------------------------------------------------------

func _draw():
	var faces_data = get_categorized_visible_faces()
	if faces_data.is_empty():
		return

	var perspective_factor = faces_data["perspective_factor"]
	var front_polygon = faces_data["front"]
	var side_faces = faces_data["sides"]
	var bottom_faces = faces_data["bottom"]
	var inner_polygons = faces_data["inner"]

	# --- Build a single list of drawable objects with sort keys ---
	var drawables = []
	var sort_dir = 1.0 if perspective_factor >= 0 else -1.0

	# Layer 1: Bottom faces
	bottom_faces.sort_custom(func(a, b):
		if a[0].y != b[0].y:
			return a[0].y < b[0].y
		else:
			return a[0].x < b[0].x
	)
	for face in bottom_faces:
		drawables.append({"poly": face, "color": side_color, "layer": 1, "sub_sort": 0})

	# Layer 0: Side faces
	for face in side_faces:
		var avg_x = (face[0].x + face[1].x) / 2.0
		drawables.append({"poly": face, "color": side_color, "layer": 0, "sub_sort": avg_x * sort_dir})

	# Layer 2: Top face
	if not front_polygon.is_empty():
		drawables.append({"poly": front_polygon, "color": top_color, "layer": 2, "sub_sort": 0})

	# Layer 3: Inner faces
	for face in inner_polygons:
		drawables.append({"poly": face, "color": inner_color, "layer": 3, "sub_sort": 0})

	# --- Sort all drawables back-to-front ---
	drawables.sort_custom(func(a, b):
		if a.layer != b.layer:
			return a.layer < b.layer
		else:
			return a.sub_sort < b.sub_sort
	)

	# --- Single drawing pass ---
	var edge_dict := {}
	for item in drawables:
		var poly = item.poly

		# 绘制填充
		if use_texture:
			var tex: Texture2D = null
			if item.layer == 2: # 顶面
				tex = top_texture
			elif item.layer == 3: # 内面
				tex = inner_texture
			elif item.layer == 1: # front(bottom) vertical faces
				tex = front_texture if front_texture != null else side_texture if side_texture != null else top_texture
			else: # 侧面和底面
				tex = side_texture if side_texture != null else front_texture if front_texture != null else top_texture

			if tex != null:
				var uvs = _calculate_uvs(poly, item.layer)
				var cols = _calculate_vertex_colors(poly, item.layer, item.color)

				# 使用 draw_primitive 替代 draw_polygon 以确保顶点颜色插值
				if poly.size() == 4: # 假定面都是四边形
					var points = poly
					var colors = cols
					var tri_uvs = uvs

					# 第一个三角形: 0, 1, 2
					var tri1_points = PackedVector2Array([points[0], points[1], points[2]])
					var tri1_colors = PackedColorArray([colors[0], colors[1], colors[2]])
					var tri1_uvs = PackedVector2Array([uvs[0], uvs[1], uvs[2]])
					draw_primitive(tri1_points, tri1_colors, tri1_uvs, tex)

					# 第二个三角形: 0, 2, 3
					var tri2_points = PackedVector2Array([points[0], points[2], points[3]])
					var tri2_colors = PackedColorArray([colors[0], colors[2], colors[3]])
					var tri2_uvs = PackedVector2Array([uvs[0], uvs[2], uvs[3]])
					draw_primitive(tri2_points, tri2_colors, tri2_uvs, tex)
				elif not poly.is_empty():
					# 对于非四边形（例如顶面），回退到 draw_polygon
					draw_polygon(poly, cols, uvs, tex)
			else:
				draw_colored_polygon(poly, item.color)

		# 收集边，用于统一描边
		if outline_thickness > 0 and not poly.is_empty():
			_collect_edges(poly, edge_dict)

	# --- 单面绘制结束，统一绘制所有可见边描边 ---
	if outline_thickness > 0:
		for seg in edge_dict.values():
			draw_line(seg[0], seg[1], outline_color, outline_thickness)

	# --- 新增：绘制顶面专用描边 (inward only) ---
	if top_outline_thickness > 0 and not front_polygon.is_empty():
		_draw_top_outline(front_polygon)

# 绘制顶面内描边，支持任意多边形形状
func _draw_top_outline(polygon: PackedVector2Array):
	if polygon.size() < 3:
		return

	var n = polygon.size()

	# 预计算多边形中心点
	var polygon_center = Vector2.ZERO
	for p in polygon:
		polygon_center += p
	polygon_center /= polygon.size()

	# 为每条边创建向内的描边四边形
	for i in range(n):
		var p1 = polygon[i]
		var p2 = polygon[(i + 1) % n]

		# 计算边的方向向量和法向量
		var edge_dir = (p2 - p1).normalized()
		var edge_normal = edge_dir.orthogonal() # 垂直向量

		# 计算边中心点到多边形中心点的方向向量
		var edge_center = (p1 + p2) * 0.5
		var to_center = (polygon_center - edge_center).normalized()

		# 选择指向内部的法向量
		var inward_normal = edge_normal
		if edge_normal.dot(to_center) < 0:
			inward_normal = -edge_normal

		# 创建向内偏移的边
		var inner_p1 = p1 + inward_normal * top_outline_thickness
		var inner_p2 = p2 + inward_normal * top_outline_thickness

		# 构建描边四边形：外边->内边
		var quad_verts = PackedVector2Array([p1, p2, inner_p2, inner_p1])

		# 绘制描边四边形
		var colors = PackedColorArray()
		colors.resize(4)
		colors.fill(top_outline_color)
		draw_polygon(quad_verts, colors)


func _calculate_vertex_colors(poly: PackedVector2Array, layer: int, base_color: Color) -> PackedColorArray:
	var cols := PackedColorArray()
	if poly.is_empty(): return cols

	var vert_color = base_color
	vert_color.b = float(layer) / 3.0

	if layer == 0: # Side faces - Horizontal Gradient
		# poly order is [inner_top, inner_bottom, outer_bottom, outer_top] from Z-sort
		# We want inner vertices (0,1) to have g=0 and outer (2,3) to have g=1
		var v_col_inner = vert_color; v_col_inner.g = 0.0; v_col_inner.r = 0.0
		var v_col_outer = vert_color; v_col_outer.g = 1.0; v_col_outer.r = 0.0
		cols.append(v_col_inner)
		cols.append(v_col_inner)
		cols.append(v_col_outer)
		cols.append(v_col_outer)

	elif layer == 1: # Bottom/Front faces - Vertical Gradient
		var poly_min_y: float = poly[0].y
		var poly_max_y: float = poly[0].y
		for i in range(1, poly.size()):
			poly_min_y = min(poly_min_y, poly[i].y)
			poly_max_y = max(poly_max_y, poly[i].y)
		var poly_height: float = poly_max_y - poly_min_y
		if poly_height < 0.001: poly_height = 1.0

		for p in poly:
			var normalized_y: float = (p.y - poly_min_y) / poly_height
			var v_col: Color = vert_color
			v_col.r = normalized_y
			v_col.g = 0.0
			cols.append(v_col)
			
	else: # Top (2) or Inner (3) faces - No gradient data needed
		for _i in range(poly.size()):
			var v_col: Color = vert_color
			v_col.r = 0.0
			v_col.g = 0.0
			cols.append(v_col)
			
	return cols

# -- Geometry Calculation ------------------------------------------------------

func _get_base_polygon() -> PackedVector2Array:
	# 确保 row_count 在 1~2 范围
	var _row_count = clamp(row_count, 1, 2)
	var grid_rows = [row_1]
	if _row_count > 1:
		grid_rows.append(row_2)
	var cell_polygons: Array[PackedVector2Array] = []
	
	var total_size = cell_size * 3
	var top_left_offset = - total_size / 2.0

	for r in range(_row_count):
		var row_mask = grid_rows[r]
		# Using bitwise flags: Left=4, Center=2, Right=1
		if row_mask & 4: # Left
			cell_polygons.append(_rect_to_poly(Rect2(top_left_offset + Vector2(0, r) * cell_size, cell_size)))
		if row_mask & 2: # Center
			cell_polygons.append(_rect_to_poly(Rect2(top_left_offset + Vector2(1, r) * cell_size, cell_size)))
		if row_mask & 1: # Right
			cell_polygons.append(_rect_to_poly(Rect2(top_left_offset + Vector2(2, r) * cell_size, cell_size)))
	
	var merged = _merge_polygons(cell_polygons)
	
	if merged.is_empty():
		return PackedVector2Array()
	else:
		# Assume we want a single contiguous shape.
		return merged[0]

func _merge_polygons(polygons: Array[PackedVector2Array]) -> Array[PackedVector2Array]:
	if polygons.size() <= 1:
		return polygons

	var merged_list = polygons.duplicate()
	var did_merge_in_pass = true
	while did_merge_in_pass:
		did_merge_in_pass = false
		var i = 0
		while i < merged_list.size():
			var j = i + 1
			while j < merged_list.size():
				var result = Geometry2D.merge_polygons(merged_list[i], merged_list[j])
				if not result.is_empty() and result.size() == 1:
					merged_list[i] = result[0]
					merged_list.remove_at(j)
					did_merge_in_pass = true
					break # Restart inner loop with the new, larger polygon
				else:
					j += 1
			if did_merge_in_pass:
				break # Restart outer loop
			i += 1
	return merged_list

func _rect_to_poly(rect: Rect2) -> PackedVector2Array:
	var p = PackedVector2Array()
	p.append(rect.position)
	p.append(rect.position + Vector2(rect.size.x, 0))
	p.append(rect.position + rect.size)
	p.append(rect.position + Vector2(0, rect.size.y))
	return p

func _calculate_uvs(polygon: PackedVector2Array, layer: int = 2) -> PackedVector2Array:
	"""为多边形计算UV坐标，基于包围盒"""
	if polygon.size() < 3:
		return PackedVector2Array()
	
	# 根据layer选择对应的texture_scale
	var scale: Vector2
	match layer:
		2: # 顶面
			scale = top_texture_scale
		3: # 内面
			scale = inner_texture_scale
		1: # front/bottom faces
			scale = front_texture_scale
		_: # 侧面
			scale = side_texture_scale
	
	# 计算包围盒
	var min_pos = polygon[0]
	var max_pos = polygon[0]
	for point in polygon:
		min_pos = min_pos.min(point)
		max_pos = max_pos.max(point)
	
	var size = max_pos - min_pos
	if size.x <= 0 or size.y <= 0:
		# 如果多边形是线或点，使用默认UV
		var uvs = PackedVector2Array()
		for i in range(polygon.size()):
			uvs.append(Vector2(i * 0.25, 0.5))
		return uvs
	
	# 为每个顶点计算UV坐标（按单元格尺寸平铺）
	var uvs = PackedVector2Array()
	for point in polygon:
		var local_pos = point - min_pos
		# 使用单元格尺寸进行缩放，保证增加单元格数量时纹理按格子平铺
		var uv_x = (local_pos.x / cell_size.x) * scale.x
		var uv_y = (local_pos.y / cell_size.y) * scale.y
		uvs.append(Vector2(uv_x, uv_y))
	
	return uvs

# 根据 y 位置(上下)为正面多边形添加透视偏移，使顶面宽度随位置产生梯形收缩/扩张
func _apply_front_perspective(poly: PackedVector2Array, perspective_shift_x: float, _perspective_factor: float) -> PackedVector2Array:
	if poly.is_empty():
		return poly
	# 以所在九宫格“行”为单位判断顶/底，避免在 L 型这类凸多边形中出现整行判断失误。
	# 计算九宫格整体左上角（与 _get_base_polygon 中一致）。
	var total_size := cell_size * 3
	var top_left_offset := -total_size / 2.0

	var result := PackedVector2Array()
	var shift_coeff := perspective_top

	for v in poly:
		# 计算该顶点处于哪一行（0–3），使用 round + clamp 消除浮点边界误差
		var row_pos: float = (v.y - top_left_offset.y) / cell_size.y # 理论应为 0.0~3.0
		var row_index: int = clamp(int(round(row_pos)), 0, 3) # 四舍五入再限制到 0–3 范围
		# 行从 0 - 3，对应偏移从 -1 → 1，中间值为 0
		var shift_value: float = (row_index - 1.5) / 1.5

		var horizontal_shift: float = perspective_shift_x * shift_coeff * shift_value
		result.append(v + Vector2(horizontal_shift, 0))

	return result

func get_categorized_visible_faces() -> Dictionary:
	var geo = _calculate_geometry()
	if geo.is_empty() or geo["front_polygon"].is_empty():
		return {}

	var front_polygon = geo["front_polygon"]
	var back_polygon = geo["back_polygon"]
	var perspective_factor = geo["perspective_factor"]

	var side_faces: Array[PackedVector2Array] = []
	var bottom_faces: Array[PackedVector2Array] = []
	for i in range(front_polygon.size()):
		var p1 = front_polygon[i]
		var p2 = front_polygon[(i + 1) % front_polygon.size()]
		var back_p1 = back_polygon[i]
		var back_p2 = back_polygon[(i + 1) % back_polygon.size()]
		var edge_normal = (p2 - p1).orthogonal().normalized()

		var is_visible = false
		if (perspective_factor > 0.01 and edge_normal.x < -0.1) or \
		   (perspective_factor < -0.01 and edge_normal.x > 0.1) or \
		   (edge_normal.y > 0.1):
			is_visible = true

		if is_visible:
			var face = PackedVector2Array([p1, p2, back_p2, back_p1])
			if edge_normal.y > 0.1:
				bottom_faces.append(face)
			else:
				side_faces.append(face)
	
	return {
		"front": front_polygon,
		"sides": side_faces,
		"bottom": bottom_faces,
		"inner": geo["inner_polygons"],
		"perspective_factor": perspective_factor
	}

func generate_outlines(shrink_factor: float = 0.0) -> Array[PackedVector2Array]:
	var categorized_faces = get_categorized_visible_faces()
	if categorized_faces.is_empty():
		return []

	var front_face: PackedVector2Array = categorized_faces.get("front", PackedVector2Array())
	var side_faces: Array[PackedVector2Array] = categorized_faces.get("sides", [])
	var bottom_faces: Array[PackedVector2Array] = categorized_faces.get("bottom", [])
	
	if front_face.is_empty() or not categorized_faces.has("perspective_factor"):
		return []

	var polygons_to_merge: Array[PackedVector2Array] = [front_face]

	var perspective_factor = categorized_faces["perspective_factor"]
	var perspective_shift_x = depth * perspective_factor
	# The shrink vector is the vector from a front vertex to its corresponding back vertex
	var total_depth_vector = Vector2(-perspective_shift_x * perspective_bottom, depth)
	var shrink_vector = total_depth_vector * shrink_factor

	var all_side_faces = side_faces + bottom_faces
	for side_face in all_side_faces:
		# side_face vertex order is [p1, p2, back_p2, back_p1]
		var shrunk_face = PackedVector2Array()
		shrunk_face.append(side_face[0]) # p1
		shrunk_face.append(side_face[1]) # p2
		shrunk_face.append(side_face[2] - shrink_vector) # shrunk back_p2
		shrunk_face.append(side_face[3] - shrink_vector) # shrunk back_p1
		polygons_to_merge.append(shrunk_face)
	
	# Use the existing merge function
	var merged_outlines = _merge_polygons(polygons_to_merge)
	return merged_outlines

func get_inner_polygons() -> Array[PackedVector2Array]:
	var geo = _calculate_geometry()
	if geo.is_empty() or geo["front_polygon"].is_empty() or not geo.has("inner_polygons"):
		return []

	var inner_polygons = geo["inner_polygons"]

	return inner_polygons


func update_texture_offset(offset: Vector2):
	self.set_instance_shader_parameter("texture_uv_offset", offset)
	self.set_instance_shader_parameter("dirt_uv_offset", offset)

func _collect_edges(poly: PackedVector2Array, edge_dict: Dictionary):
	var n := poly.size()
	if n < 2:
		return
	for i in range(n):
		var p1 := poly[i]
		var p2 := poly[(i + 1) % n]
		var key := _edge_key(p1, p2)
		if not edge_dict.has(key):
			edge_dict[key] = [p1, p2]

func _edge_key(p1: Vector2, p2: Vector2) -> String:
	# 通过对坐标进行排序并四舍五入到 0.01，生成无方向边的唯一键
	var a := p1
	var b := p2
	if a.x > b.x or (is_equal_approx(a.x, b.x) and a.y > b.y):
		var tmp = a
		a = b
		b = tmp
	# 四舍五入，减少浮点误差
	var ax = snapped(a.x, 0.01)
	var ay = snapped(a.y, 0.01)
	var bx = snapped(b.x, 0.01)
	var by = snapped(b.y, 0.01)
	return "%0.2f,%0.2f-%0.2f,%0.2f" % [ax, ay, bx, by]