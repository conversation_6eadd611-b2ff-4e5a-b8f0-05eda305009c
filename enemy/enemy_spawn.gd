class_name EnemySpawn
extends Node2D

var enemy_prefab: PackedScene = preload("res://enemy/prefab/enemy.tscn")
@export var grid_cols := 8
@export var grid_rows := 3
@export var grid_cell_width := 90
@export var grid_cell_height := 100
@export var spawn_y := 0
@export var min_spawn_interval := 3.0 # 两波次之间的最小间隔
@export var debug_draw: bool = true
@export var pre_occupied_timeout := 10.0 # 预占用的超时时间（秒）

# 定义单元格的三种状态（作为位掩码使用）
enum CellState {
	FREE = 0,
	OCCUPIED = 1,
	PRE_OCCUPIED = 2
}

var enemy_spawn_configs: Array[EnemySpawnConfig] = []
var total_weight: int = 0

var spawn_grid: Array = []
var active_enemies: Array[Dictionary] = []
var pre_occupied_areas: Array[Dictionary] = [] # 存储预占用的区域信息
var last_spawn_time := 0.0 # 上次孵化的时间戳 (msec)
var last_spawned_wave: Array[EnemyBase] = []

signal enemy_spawned(enemy: EnemyBase)
# 为预占用状态的视觉效果预留的信号接口
signal area_pre_occupied(grid_rect: Rect2i)
signal pre_occupation_cancelled(grid_rect: Rect2i)


func _ready() -> void:
	# 从GameManager获取当前主题的敌人配置
	if GameManager.current_theme and not GameManager.current_theme.enemy_spawn_configs.is_empty():
		self.enemy_spawn_configs = GameManager.current_theme.enemy_spawn_configs
	else:
		push_error("EnemySpawn: No enemy configs found in the current theme or no theme is set.")
		# 可选：在这里加载一个默认的敌人配置以防出错
		return

	for _r in range(grid_rows):
		var row: Array[int] = []
		row.resize(grid_cols)
		row.fill(CellState.FREE)
		spawn_grid.append(row)

	for config in enemy_spawn_configs:
		total_weight += config.weight

	# 初始化上次生成时间，确保游戏一开始就能生成第一波
	last_spawn_time = -min_spawn_interval * 1000.0


func _process(_delta):
	# 阶段 A: 更新占用网格并绘图
	_update_occupied_grid()
	if debug_draw:
		queue_redraw()

	# 阶段 B: 检查并处理预占用区域 (超时和生成)
	_process_pre_occupied_areas()

	# 阶段 D: 如果条件满足，尝试孵化新的一波敌人
	if _can_spawn_new_wave():
		_try_spawn_wave()


func _draw() -> void:
	if not debug_draw:
		return

	for r in range(grid_rows):
		for c in range(grid_cols):
			var rect = Rect2(
				c * grid_cell_width,
				spawn_y + r * grid_cell_height,
				grid_cell_width,
				grid_cell_height
			)

			var color: Color
			var cell_state = spawn_grid[r][c]

			var is_occupied = (cell_state & CellState.OCCUPIED) != 0
			var is_pre_occupied = (cell_state & CellState.PRE_OCCUPIED) != 0

			if is_occupied:
				color = Color(1, 0, 0, 0.4) # 红色: 已占用 (最高优先级)
			elif is_pre_occupied:
				color = Color(1, 1, 0, 0.4) # 黄色: 预占用
			else:
				color = Color(0, 1, 0, 0.4) # 绿色: 空闲

			draw_rect(rect, color)
			draw_rect(rect, Color.WHITE, false, 1.0)


# ==================== 新的波次生成核心逻辑 ====================

func _can_spawn_new_wave() -> bool:
	# 条件1: 必须超过最小生成间隔
	if Time.get_ticks_msec() - last_spawn_time < min_spawn_interval * 1000:
		return false

	return true

func _try_spawn_wave() -> void:
	# 1. 规划这一波要生成的敌人
	var planned_wave: Array[Dictionary] = _plan_enemy_wave()
	if planned_wave.is_empty():
		return # 没规划出东西，直接返回

	# 2. 更新时间戳，并准备记录新生成的敌人
	last_spawn_time = Time.get_ticks_msec()
	var spawned_enemies: Array[EnemyBase] = []

	var spawn_row_index = grid_rows - 1

	# 3. 遍历规划好的敌人，尝试放置它们（部分生成 + 部分预占）
	for enemy_data in planned_wave:
		var grid_config = enemy_data.grid_config
		var dimensions = enemy_data.dimensions
		var enemy_config = enemy_data.enemy_config

		# 3a. 寻找完美位置
		var perfect_spot: Vector2i = _find_perfect_free_spot(dimensions, spawn_row_index)
		if perfect_spot.x != -1:
			var grid_rect = Rect2i(perfect_spot, dimensions)
			var enemy: EnemyBase = _instantiate_enemy(grid_config, grid_rect, enemy_config)
			spawned_enemies.append(enemy)
			continue # 生成成功，处理下一个

		# 3b. 未找到完美位置，寻找最佳位置进行预占用
		var best_spot_info: Dictionary = _find_best_pre_occupied_spot(dimensions, spawn_row_index)
		if not best_spot_info.is_empty() and best_spot_info.overlap > 0:
			var grid_rect = Rect2i(best_spot_info.position, dimensions)
			_mark_grid_from_rect(grid_rect, CellState.PRE_OCCUPIED, true)
			pre_occupied_areas.append({
				"grid_config": grid_config,
				"grid_rect": grid_rect,
				"timestamp": Time.get_ticks_msec(),
				"enemy_config": enemy_config
			})
			area_pre_occupied.emit(grid_rect)

	# 4. 如果本次有任何敌人被立即生成，则设置等待状态
	if not spawned_enemies.is_empty():
		last_spawned_wave = spawned_enemies


func _plan_enemy_wave() -> Array:
	var base_width = grid_cols * clampf(GameManager.get_enemy_spawn_rate(), 0.3, 1.0)
	var random_factor = randf_range(0.7, 1.3) # 增加70%-130%的随机变化
	var target_width = clampi(base_width * random_factor, 1, grid_cols)

	var current_width: int = 0
	var planned_wave: Array[Dictionary] = []

	var attempts: int = 0
	var max_attempts = grid_cols * 2 # 防止死循环
	while current_width < target_width and attempts < max_attempts:
		attempts += 1
		var config: EnemySpawnConfig = _get_random_enemy_config()
		if config == null:
			continue
		var grid_config: Array[int] = EnemyVis.generate_grid_config(config.min_cells, config.max_cells)
		var metrics: Dictionary = EnemyVis.get_grid_metrics(grid_config)
		var dimensions = metrics.dimensions
		if dimensions.x == 0 or dimensions.y == 0:
			continue

		# 如果当前敌人能放下，就加入规划列表
		if current_width + dimensions.x <= target_width:
			planned_wave.append({"grid_config": grid_config, "dimensions": dimensions, "enemy_config": config})
			current_width += dimensions.x

	return planned_wave

# ==================== 占用及孵化逻辑 ====================

# 此函数现在负责扫描所有战场对象并更新 OCCUPIED 状态
func _update_occupied_grid():
	# 1. 清除所有格子的 OCCUPIED 标志位
	for r in range(grid_rows):
		for c in range(grid_cols):
			spawn_grid[r][c] &= ~CellState.OCCUPIED

	# 2. 遍历所有活动敌人，重新设置它们的 OCCUPIED 标志位
	var remaining_enemies: Array[Dictionary] = []
	for data in active_enemies:
		var enemy: EnemyBase = data["enemy"]
		if not is_instance_valid(enemy):
			continue
		_mark_enemy_as_occupied(enemy)
		remaining_enemies.append(data)
	active_enemies = remaining_enemies

	# 3. 遍历所有活动弹球，标记它们的位置
	for ball:BallBase in BallPoolManager.balls:
		if is_instance_valid(ball) and ball.is_active:
			_mark_canvas_item_as_occupied(ball)


# 根据敌人的精确格子配置，将其在网格中占据的单元格标记为"占用"
func _mark_enemy_as_occupied(enemy: EnemyBase) -> void:
	# 1. 获取敌人的格子配置与形状度量
	var grid_config: Array[int] = enemy.grid_config
	if grid_config.is_empty():
		return
	var metrics: Dictionary = EnemyVis.get_grid_metrics(grid_config)
	var dimensions: Vector2i = metrics.dimensions
	if dimensions == Vector2i.ZERO:
		return

	# 2. 计算敌人中心点在孵化网格中的浮点坐标（单位：格）
	var enemy_local_pos: Vector2 = self.to_local(enemy.global_position) - Vector2(0, spawn_y)
	var col_f: float = enemy_local_pos.x / grid_cell_width
	var row_f: float = enemy_local_pos.y / grid_cell_height

	# 3. 形状自身的中心（以九宫格坐标系计）
	var shape_center_col: float = metrics.min_col + float(dimensions.x) / 2.0
	var shape_center_row: float = metrics.min_row + float(dimensions.y) / 2.0

	# 4. 遍历形状中的每个被占用格子，将其映射到孵化网格中，并考虑半格跨越
	for r_off in range(dimensions.y):
		var row_idx: int = metrics.min_row + r_off
		var row_mask: int = grid_config[row_idx]
		for c_off in range(dimensions.x):
			var col_idx: int = metrics.min_col + c_off
			var col_mask: int = int(pow(2, 2 - col_idx))
			if row_mask & col_mask:
				# 4a. 计算此格子中心相对敌人中心的偏移（单位：格）
				var cell_row_offset: float = (row_idx + 0.5) - shape_center_row
				var cell_col_offset: float = (col_idx + 0.5) - shape_center_col

				# 4b. 计算此格子中心在孵化网格中的浮点坐标
				var cell_center_row_f: float = row_f + cell_row_offset
				var cell_center_col_f: float = col_f + cell_col_offset

				# 4c. 根据中心浮点坐标推断该格子覆盖的行区间（只考虑垂直跨格）
				var top_r: int = int(floor(cell_center_row_f - 0.5))
				var bottom_r: int = int(floor(cell_center_row_f + 0.5))
				# 水平方向只取所在列，不做跨列处理，避免 1x1 横向占 2 格
				var grid_c: int = int(floor(cell_center_col_f))

				# 4d. 标记所有实际被覆盖到的孵化网格单元（列固定，行可能 1~2）
				for gr in range(top_r, bottom_r + 1):
					if gr < 0 or gr >= grid_rows:
						continue
					if grid_c >= 0 and grid_c < grid_cols:
						spawn_grid[gr][grid_c] |= CellState.OCCUPIED


# 根据节点的视觉边界，将其在网格中占据的单元格标记为"占用"
func _mark_canvas_item_as_occupied(node: CanvasItem):
	var world_rect: Rect2
	if node is BallBase:
		world_rect = node.get_rect()
	else:
		world_rect = node.get_global_transform() * node.get_rect()

	var local_pos = world_rect.position - Vector2(0, spawn_y)

	var start_col = floor(local_pos.x / grid_cell_width)
	var end_col = floor((local_pos.x + world_rect.size.x - 0.1) / grid_cell_width)
	var start_row = floor(local_pos.y / grid_cell_height)
	var end_row = floor((local_pos.y + world_rect.size.y - 0.1) / grid_cell_height)

	for r in range(max(0, start_row), min(grid_rows - 1, end_row) + 1):
		for c in range(max(0, start_col), min(grid_cols - 1, end_col) + 1):
			if r < grid_rows and c < grid_cols:
				spawn_grid[r][c] |= CellState.OCCUPIED


func _process_pre_occupied_areas():
	var remaining_pre_occupied: Array[Dictionary] = []
	var current_time = Time.get_ticks_msec()

	for area_data in pre_occupied_areas:
		var grid_rect: Rect2i = area_data.grid_rect

		# 检查超时
		if current_time - area_data.timestamp > pre_occupied_timeout * 1000:
			_mark_grid_from_rect(grid_rect, CellState.PRE_OCCUPIED, false) # 清除 PRE_OCCUPIED 标志位
			pre_occupation_cancelled.emit(grid_rect)
			continue # 超时，放弃这个区域

		# 检查区域是否已经清空
		if _is_area_clear_for_spawning(grid_rect):
			# 条件满足，生成敌人
			_instantiate_enemy(area_data.grid_config, grid_rect, area_data.enemy_config)
		else:
			# 条件不满足，保留在列表中继续等待
			remaining_pre_occupied.append(area_data)

	pre_occupied_areas = remaining_pre_occupied

# 检查指定区域内是否还有任何"占用"状态的格子
func _is_area_clear_for_spawning(grid_rect: Rect2i) -> bool:
	for r in range(grid_rect.position.y, grid_rect.position.y + grid_rect.size.y):
		for c in range(grid_rect.position.x, grid_rect.position.x + grid_rect.size.x):
			if r < grid_rows and c < grid_cols:
				if (spawn_grid[r][c] & CellState.OCCUPIED) != 0:
					return false # 区域内有东西，不清空
	return true # 区域内没有被占用的格子，是清空的


func _find_perfect_free_spot(dimensions: Vector2i, row_index: int) -> Vector2i:
	# 敌人的"脚"部在孵化行，身体向上延伸
	var start_row = row_index - dimensions.y + 1
	if start_row < 0:
		return Vector2i(-1, -1) # 敌人太高，无法在此生成

	var possible_spots: Array[Vector2i] = []
	for c in range(grid_cols - dimensions.x + 1):
		# 检查从 (c, start_row) 开始的矩形区域是否完全空闲
		if _is_spot_fully_free(c, start_row, dimensions.x, dimensions.y):
			# 为不同位置分配不同权重，增加随机性
			var edge_factor: float = 1.0
			# 如果靠近中央，增加权重
			if c >= 2 and c <= grid_cols - dimensions.x - 2:
				edge_factor = 5.0
			# 根据权重多次添加到候选列表，增加被选中概率
			for i in range(int(edge_factor)):
				possible_spots.append(Vector2i(c, start_row))

	if possible_spots.is_empty():
		return Vector2i(-1, -1)
	return possible_spots.pick_random()

func _find_best_pre_occupied_spot(dimensions: Vector2i, row_index: int) -> Dictionary:
	var best_spot: Dictionary = {"position": Vector2i(-1, -1), "overlap": 0}

	var start_row = row_index - dimensions.y + 1
	if start_row < 0:
		return {} # 敌人太高

	for c in range(grid_cols - dimensions.x + 1):
		var candidate_pos = Vector2i(c, start_row)
		var candidate_rect = Rect2i(candidate_pos, dimensions)

		# 确保候选区域不与任何已存在的预占用区域重叠
		var overlaps_existing_pre_occupation: bool = false
		for y_check in range(candidate_rect.position.y, candidate_rect.end.y):
			for x_check in range(candidate_rect.position.x, candidate_rect.end.x):
				if (spawn_grid[y_check][x_check] & CellState.PRE_OCCUPIED) != 0:
					overlaps_existing_pre_occupation = true
					break
			if overlaps_existing_pre_occupation:
				break

		if overlaps_existing_pre_occupation:
			continue # 如果重叠，则跳过此候选位置

		var current_overlap: int = 0
		# 计算当前位置与"空闲"格子的重叠度
		for y in range(dimensions.y):
			for x in range(dimensions.x):
				var check_r = candidate_pos.y + y
				var check_c = candidate_pos.x + x
				if spawn_grid[check_r][check_c] == CellState.FREE:
					current_overlap += 1

		if current_overlap > best_spot.overlap:
			best_spot.position = candidate_pos
			best_spot.overlap = current_overlap

	return best_spot

func _is_spot_fully_free(start_col: int, start_row: int, width: int, height: int) -> bool:
	for r in range(start_row, start_row + height):
		for c in range(start_col, start_col + width):
			if spawn_grid[r][c] != CellState.FREE:
				return false
	return true

func _instantiate_enemy(grid_config: Array[int], grid_rect: Rect2i, enemy_config: EnemySpawnConfig) -> EnemyBase:
	var grid_pos: Vector2i = grid_rect.position
	var grid_size: Vector2i = grid_rect.size

	# 将格子标记为已占用, 并清除预占用标志
	_mark_grid_from_rect(grid_rect, CellState.PRE_OCCUPIED, false) # 清除 PRE_OCCUPIED
	_mark_grid_from_rect(grid_rect, CellState.OCCUPIED, true)    # 设置 OCCUPIED

	var check_pos_center = Vector2(
		(grid_pos.x + float(grid_size.x) / 2.0) * grid_cell_width,
		spawn_y + (grid_pos.y + float(grid_size.y) / 2.0) * grid_cell_height
	)

	var enemy: EnemyBase = enemy_prefab.instantiate()
	add_child(enemy)
	# 订阅敌人的销毁信号
	enemy.destroying.connect(_on_enemy_destroying)

	enemy.position = check_pos_center + Vector2(randf_range(-2, 2), 0)
	enemy.init_with_config(grid_config, enemy_config)

	# 应用当前主题的纹理
	if GameManager.current_theme:
		enemy.apply_theme_textures(GameManager.current_theme)

	active_enemies.append({"enemy": enemy, "grid_rect": grid_rect})
	enemy_spawned.emit(enemy)
	return enemy


# 响应敌人的销毁信号，将其从活动列表中移除
func _on_enemy_destroying(enemy_to_remove: EnemyBase) -> void:
	for i in range(active_enemies.size() - 1, -1, -1):
		var data: Dictionary = active_enemies[i]
		if data.enemy == enemy_to_remove:
			active_enemies.remove_at(i)
			return # 找到并移除后即可退出


# ==================== Helper Functions ====================

func _get_random_enemy_config() -> EnemySpawnConfig:
	if total_weight == 0:
		# 如果 total_weight 为 0，可能是因为没有正确加载配置，返回一个安全的默认值
		if not enemy_spawn_configs.is_empty():
			return enemy_spawn_configs[0]
		else:
			push_error("Cannot get random enemy config: no configs available.")
			return null # 或者返回一个有效的默认字典

	var rand_val = randi() % total_weight
	# 按权重返回
	var cumulative_weight: int = 0
	for config in enemy_spawn_configs:
		cumulative_weight += config.weight
		if rand_val < cumulative_weight:
			return config
	return enemy_spawn_configs[-1]

func _mark_grid(start_col: int, start_row: int, width: int, height: int, flag: int, set_flag: bool):
	for r in range(start_row, start_row + height):
		if r >= grid_rows: continue
		for c in range(start_col, start_col + width):
			if c >= grid_cols: continue
			if set_flag:
				spawn_grid[r][c] |= flag # 设置标志位
			else:
				spawn_grid[r][c] &= ~flag # 清除标志位


func _mark_grid_from_rect(rect: Rect2i, flag: int, set_flag: bool):
	_mark_grid(rect.position.x, rect.position.y, rect.size.x, rect.size.y, flag, set_flag)

func _is_row_clear(row_index: int) -> bool:
	if row_index >= grid_rows: return true
	for col_occupied in spawn_grid[row_index]:
		if col_occupied != CellState.FREE:
			return false
	return true
