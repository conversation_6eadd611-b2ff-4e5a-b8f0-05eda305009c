@tool
class_name EnemyBase
extends AnimatableBody2D

signal destroying(enemy)


# 单元攻击状态类
class AttackState:
	var attack_timer: float = 0.0
	var next_attack_interval: float = 0.0
	var unit: EnemyUnit


	func _init(enemy_unit: EnemyUnit):
		unit = enemy_unit
		reset_attack_timer()


	func reset_attack_timer():
		if unit:
			next_attack_interval = randf_range(unit.attack_interval_min, unit.attack_interval_max)
		else:
			next_attack_interval = randf_range(3.0, 6.0) # 默认值
		attack_timer = 0.0


	func update_timer(delta: float) -> bool:
		attack_timer += delta
		return attack_timer >= next_attack_interval


	func can_attack_player(player_pos: Vector2, unit_world_pos: Vector2) -> bool:
		if not unit:
			return false
		var distance = unit_world_pos.distance_to(player_pos)
		return distance <= unit.attack_range

# 敌人类型枚举
enum EnemyType {
	SQUARE,
}
@export_tool_button("generate_enemy_debug") var generate_enemy_debug_btn: Callable = _generate_enemy_debug
@export var enemy_type: EnemyType = EnemyType.SQUARE
@export var hit_duration: float = 0.2
@export var experience: float = 10.0

var grid_config: Array[int] = []

@onready var sprite: ShadersSprite2d = $Sprite2D
@onready var attribute_component: AttributeComponent = $AttributeComponent
@onready var subviewport: SubViewport = $SubViewport
@onready var enemy_vis: EnemyVis = %EnemyVis
@onready var box: BoxShape = enemy_vis.get_node("BoxShape")
@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var shadow: Node2D = $Shadow

@export var buff_handlers: Array[BuffHandlerBase] = []

var _handler_map: Dictionary = {}
var is_dead: bool = false
var hit_tween: Tween
var velocity: Vector2 = Vector2.ZERO
# 统一攻击管理系统
var attack_enabled: bool = false
var player_target: Node2D = null
var attackable_units: Array[EnemyUnit] = []
# 为每个单元维护独立的攻击状态
var unit_attack_states: Dictionary = {} # key: EnemyUnit, value: AttackState

var hp:
	get:
		return attribute_component.get_attribute_value("hp")
	set(value):
		attribute_component.find_attribute("hp").set_value(value)

var max_hp:
	get:
		return attribute_component.get_attribute_value("max_hp")
	set(value):
		attribute_component.find_attribute("max_hp").set_value(value)

var damage:
	get:
		return attribute_component.get_attribute_value("damage")
	set(value):
		attribute_component.find_attribute("damage").set_value(value)

var speed:
	get:
		return attribute_component.get_attribute_value("speed")
	set(value):
		attribute_component.find_attribute("speed").set_value(value)

var score: int:
	get:
		return attribute_component.get_attribute_value("score") as int


func _ready():
	if not Engine.is_editor_hint():
		add_to_group("enemy")

		# 获取材质实例
		if sprite:
			sprite.set_shader_param_by_name("hit_flash", "rate", 0)

		# 将数组转为字典，方便用buff_name快速查找
		for handler in buff_handlers:
			if handler and not handler.handled_buff_name.is_empty():
				_handler_map[handler.handled_buff_name] = handler

		var hp_attribute: Attribute = find_attribute("hp")
		hp_attribute.buff_added.connect(_on_buff_added)
		hp_attribute.buff_removed.connect(_on_buff_removed)

		set_physics_process(true) # 启用物理帧处理


# ===================== 生命周期方法 =====================

func _physics_process(delta):
	# AnimatableBody2D不支持linear_velocity，需手动实现移动
	if not is_dead:
		position += velocity * delta

		# 更新攻击系统
		if attack_enabled:
			_update_attack_system(delta)


# 新的初始化方法：根据给定的形状数据来构建敌人
func init_with_config(_grid_config: Array[int], _enemy_config: EnemySpawnConfig):
	self.grid_config = _grid_config

	# 确保 enemy_vis 节点已经准备好
	if not is_instance_valid(enemy_vis):
		await ready # 如果尚未 ready，等待信号

	# 调用 EnemyVis 的新方法来应用形状配置
	enemy_vis.generate_from_config(_grid_config, _enemy_config)

	# 更新视口和碰撞体
	perspective_calc()
	update_viewport_size()
	generate_collision_shape()
	generate_shadow()

	# 初始化攻击系统
	_init_attack_system()

	jump_in_screen()


func apply_theme_textures(theme: EnvironmentTheme) -> void:
	if not is_instance_valid(box) or not theme:
		return

	box.top_texture = theme.top_texture
	box.side_texture = theme.side_texture
	box.front_texture = theme.front_texture
	box.inner_texture = theme.inner_texture
	box.use_texture = true # 确保启用纹理绘制


# ===================== 攻击逻辑 =====================
func attack():
	# 原: self.linear_velocity = Vector2(0, 0)
	# 现在: 直接归零自定义速度变量
	velocity = Vector2.ZERO
	call_deferred("_clear_collision")
	shake_effect(
		func ():
			GameEvents.enemy_attacked.emit(self)
			var particle = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.ENEMY_ATTACK, self.global_position)
			particle.restart()
			destroying.emit(self)
			queue_free()
	)


func get_damage() -> int:
	return damage


func shake_effect(on_complete: Callable):
	var shake_offset: float = 15.0
	var shake_time: float = 0.05
	var shake_count: int = 10
	var tween = create_tween()
	var original_pos = position
	for i in range(shake_count):
		var decay = 1.0 - float(i) / shake_count # 幅度递减
		var angle = randf() * TAU # 随机方向
		var offset = Vector2(cos(angle), sin(angle)) * shake_offset * decay
		tween.tween_property(self, "position", original_pos + offset, shake_time)
	tween.tween_property(self, "position", original_pos, shake_time)
	if on_complete != null:
		tween.finished.connect(on_complete)


# ===================== 伤害处理 =====================
func take_damage(taken_damage: float, color: Color = Color.WHITE, source: Object = null, alread_calc: bool = false, is_crit: bool = false) -> bool:
	if is_dead:
		return false
	if not alread_calc:
		hp -= taken_damage

	var damage_info: Dictionary = {
		"amount": taken_damage,
		"color": color,
		"source": source,
		"crit": is_crit
	}
	GameEvents.enemy_received_damage.emit(self, damage_info)

	if hp <= 0:
		is_dead = true
		# 停用所有敌人单元的攻击
		_deactivate_all_units()
		call_deferred("_clear_collision")
		destroying.emit(self)
		GameEvents.enemy_died.emit(self, source)
		if source != null and source.has_method("handle_enemy_death_effect"):
			var handled = source.handle_enemy_death_effect(self)
			if not handled:
				play_death_animation()
		else:
			play_death_animation()
		return true
	else:
		# 播放受击特效
		if source != null and source is BallBase:
			play_hit_effect(color, (source.global_position - self.global_position).normalized())
		else:
			play_hit_effect(color)
		return false


# ===================== 特效播放 =====================
func play_hit_effect(color: Color = Color.WHITE, dir: Vector2 = Vector2.UP):
	if hit_tween:
		hit_tween.kill()
	hit_tween = create_tween()
	hit_tween.tween_method(set_hit_rate, 0.0, 1.0, hit_duration / 2)
	hit_tween.tween_method(set_hit_rate, 1.0, 0.0, hit_duration / 2)
	enemy_vis.shake(dir, 0.8)


func set_hit_rate(rate):
	sprite.set_shader_param_by_name("hit_flash", "rate", rate)


#==================== 死亡效果 =====================
func play_death_animation():
	var tween = create_tween().set_parallel()

	var duration: float = 0.5
	var jump_height = randf_range(100, 150)
	# 随机旋转角度，例如在360到720度之间
	var total_rotation = randf_range(20, 80) * sign(randf_range(-1, 1))

	var particleJump = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.NORMAL_DIE_JUMP, self.global_position)
	var particleJumpSize = enemy_vis.get_visual_bounds().size
	particleJump.bounds = Rect2(0.5, 0.5, particleJumpSize.x, particleJumpSize.y)

	# 1. 位置动画 (序列)
	var pos_tween = create_tween()
	var start_y = global_position.y
	# 弹起阶段: 使用SINE曲线缓出，模拟更平滑的减速到顶点
	pos_tween.tween_property(self, "global_position:y", start_y - jump_height, duration * 0.3).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_OUT)
	# 下落阶段: 使用QUAD曲线缓入，模拟重力加速，并确保落到屏幕外
	pos_tween.tween_property(self, "global_position:y", start_y, duration * 0.7).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)

	# 2. 旋转动画 (序列)
	var rot_tween = create_tween()
	# 前半段快速旋转80%的角度
	rot_tween.tween_property(self, "rotation_degrees", total_rotation * 0.3, duration * 0.2).as_relative().set_ease(Tween.EASE_OUT)
	# 后半段缓慢旋转剩下20%的角度
	rot_tween.tween_property(self, "rotation_degrees", total_rotation * 0.7, duration * 0.8).as_relative().set_ease(Tween.EASE_IN)

	# 3. 缩放动画 (序列)
	var scale_tween = create_tween()
	# 前半段快速缩小到0.5
	scale_tween.tween_property(self, "scale", Vector2(randf_range(1.0, 1.3), randf_range(1.0, 1.3)), duration * 0.2).set_ease(Tween.EASE_OUT)
	# 后半段缓慢缩小到0
	scale_tween.tween_property(self, "scale", Vector2.ONE, duration * 0.8).set_ease(Tween.EASE_IN)

	# 将所有子补间并行播放
	tween.tween_subtween(pos_tween)
	tween.tween_subtween(rot_tween)
	tween.tween_subtween(scale_tween)

	# 动画结束后释放节点
	tween.finished.connect(
		func die_effect() -> void:
			var particleDie = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.NORMAL_DIE, self.global_position)
			var particleDieSize = enemy_vis.get_visual_bounds().size
			particleDie.bounds = Rect2(0.7, 0.7, particleDieSize.x * 1.5, particleDieSize.y * 1.5)
			queue_free()
	)


# ===================== 特效播放 =====================
func _get_random_position_on_sprite() -> Vector2:
	if not sprite:
		return global_position

	var rect = sprite.get_rect()
	var random_x = randf_range(rect.position.x, rect.end.x)
	var random_y = randf_range(rect.position.y, rect.end.y)

	# 将精灵的局部随机坐标转换为全局坐标
	return sprite.to_global(Vector2(random_x, random_y))


func jump_in_screen() -> void:
	sprite.position = Vector2(0, -300)
	sprite.rotation_degrees = randf_range(-20.0, 20.0)

	shadow.scale = Vector2(0.2, 0.2)
	shadow.modulate.a = 0.2
	var wait_time = randf_range(0.0, 0.5)
	var jump_in_time: float = 0.5

	var shadow_tween = create_tween().set_parallel(true)
	# 阴影动画：缩放和淡入
	shadow_tween.tween_property(shadow, "scale", Vector2(1.0, 1.0), jump_in_time + wait_time)
	shadow_tween.tween_property(shadow, "modulate:a", 0.6, jump_in_time + wait_time)
	await get_tree().create_timer(wait_time).timeout

	var tween = create_tween().set_parallel(true)
	# 使用 EASE_IN 和 TRANS_QUAD 来模拟重力，使下落动画加速
	tween.tween_property(sprite, "position", Vector2.ZERO, jump_in_time).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_IN)
	# 使用 EASE_OUT 让旋转动画在结束时减速，效果更自然
	tween.tween_property(sprite, "rotation_degrees", 0, jump_in_time).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)

	# 等待入场动画完成
	await tween.finished
	shadow.scale = Vector2(1, 1)
	shadow.modulate.a = 0.4

	var particle = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.NORMAL_DIE_JUMP, self.global_position)
	var particleSize = enemy_vis.get_visual_bounds().size
	particle.bounds = Rect2(0.7, 0.7, particleSize.x, particleSize.y)
	# 执行震动并等待其完成
	enemy_vis.shake(Vector2.UP, 1.2)
	await enemy_vis.shake_finished

	# 动画全部结束后，才设置速度
	animation_player.play("default")
	velocity = Vector2(0, speed)


func generate_shadow():
	for child in shadow.get_children():
		child.queue_free()

	var box_polygon: Array[PackedVector2Array] = box.generate_outlines(-0.8)

	var bounds: Rect2 = enemy_vis.get_visual_bounds()
	var visual_offset = - bounds.position - (bounds.size / 2.0)

	for final_poly in box_polygon:
		var transformed_poly = PackedVector2Array()
		for point in final_poly:
			transformed_poly.append(point + visual_offset)

		var polygon_node = Polygon2D.new()
		polygon_node.polygon = transformed_poly
		shadow.add_child(polygon_node)
		if Engine.is_editor_hint():
			polygon_node.set_owner(get_tree().edited_scene_root)


# ===================== 状态管理 =====================
func _clear_collision():
	self.collision_layer = 0
	self.collision_mask = 0
	self.velocity = Vector2.ZERO


# ===================== Buff处理 =====================
func add_buff(buff: AttributeBuff):
	find_attribute("hp").add_buff(buff)


func _on_buff_added(attribute: Attribute, buff: AttributeBuff):
	if _handler_map.has(buff.buff_name):
		var handler = _handler_map[buff.buff_name]
		handler.on_buff_added(self, buff)


func _on_buff_removed(attribute: Attribute, buff: AttributeBuff):
	if _handler_map.has(buff.buff_name):
		var handler = _handler_map[buff.buff_name]
		handler.on_buff_removed(self, buff)


func get_attribute_value(attribute_name: String) -> float:
	return attribute_component.get_attribute_value(attribute_name)


func find_attribute(attribute_name: String) -> Attribute:
	return attribute_component.find_attribute(attribute_name)


func get_visual_shape_offset() -> Vector2:
	if is_instance_valid(enemy_vis):
		return enemy_vis.get_shape_offset_in_bounds()
	return Vector2.ZERO


func get_footprint_offset() -> Vector2:
	if is_instance_valid(enemy_vis):
		return enemy_vis.get_footprint_offset_from_visual_center()
	return Vector2.ZERO


func perspective_calc() -> void:
	# 透视计算
	if not Engine.is_editor_hint():
		if not is_instance_valid(box):
			return

		var main_viewport_rect = get_viewport().get_visible_rect()
		var virtual_global_position = sprite.global_position
		var h_offset = ((virtual_global_position.x - main_viewport_rect.size.x / 2.0) / main_viewport_rect.size.x) * 0.5
		var perspective_factor = clamp(h_offset, -0.8, 0.8)
		box.set_perspective_factor(perspective_factor)


func update_viewport_size() -> void:
	if not is_instance_valid(enemy_vis):
		return
	# 1. 获取 EnemyVis 的视觉边界
	var bounds: Rect2 = enemy_vis.get_visual_bounds()

	# 2. 根据边界设置 SubViewport 的尺寸，并增加一点边距以防万一
	var padding = Vector2(100, 100)
	subviewport.size = (bounds.size + padding).round()

	# 3. 将 EnemyVis 移动到 SubViewport 的中心
	# bounds.position 是左上角，我们需要将其偏移到视口中心
	enemy_vis.position = - bounds.position + (subviewport.size / 2.0 - bounds.size / 2.0)


func generate_collision_shape() -> void:
	# 1. 清除旧的碰撞体，以防重复生成
	for child in get_children():
		if child is CollisionShape2D or child is CollisionPolygon2D:
			child.queue_free()

	# 2. 从BoxShape获取最终的、经过深度收缩和合并的碰撞轮廓
	var collision_outlines: Array[PackedVector2Array] = box.generate_outlines(0.5)

	if collision_outlines.is_empty():
		push_warning("无法生成碰撞体，因为BoxShape没有返回任何碰撞轮廓。")
		return

	# 3. 获取视觉边界和偏移量以对齐碰撞体
	var bounds: Rect2 = enemy_vis.get_visual_bounds()
	var visual_offset = - bounds.position - (bounds.size / 2.0)

	# 4. 为每个最终轮廓创建一个对齐的CollisionPolygon2D
	for final_poly in collision_outlines:
		# 应用视觉偏移，使碰撞体与精灵对齐
		var transformed_poly = PackedVector2Array()
		for point in final_poly:
			transformed_poly.append(point + visual_offset)

		var collision_polygon_node = CollisionPolygon2D.new()
		collision_polygon_node.polygon = transformed_poly
		add_child(collision_polygon_node)
		if Engine.is_editor_hint():
			collision_polygon_node.set_owner(get_tree().edited_scene_root)


func generate_enemy_vis(min_cells: int = 1, max_cells: int = 4):
	enemy_vis.min_cells = min_cells
	enemy_vis.max_cells = max_cells
	enemy_vis.generate_random_enemy()
	perspective_calc()
	update_viewport_size()
	generate_collision_shape()
	generate_shadow()


func _generate_enemy_debug():
	generate_enemy_vis()
	sprite.generate()


## 停用所有敌人单元的攻击（当敌人死亡时调用）
func _deactivate_all_units() -> void:
	if not enemy_vis:
		return

	# 停用所有可攻击单元
	for unit in attackable_units:
		if is_instance_valid(unit):
			unit.deactivate()

	# 停用攻击系统
	attack_enabled = false
	attackable_units.clear()
	unit_attack_states.clear()


# ===================== 统一攻击管理系统 =====================

## 初始化攻击系统
func _init_attack_system() -> void:
	if not enemy_vis:
		return

	# 收集所有可攻击的敌人单元
	attackable_units.clear()
	unit_attack_states.clear()

	# 遍历所有子节点，包括 MaskClipper 下的单元
	_collect_attackable_units(enemy_vis)

	# 如果有可攻击单元，启用攻击系统
	if not attackable_units.is_empty():
		attack_enabled = true
		_find_player_target()

		# 为每个单元创建攻击状态
		for unit in attackable_units:
			unit_attack_states[unit] = AttackState.new(unit)
			# 禁用单元自己的攻击系统，改为由基础节点统一管理
			unit.can_attack = false


## 递归收集所有可攻击的敌人单元
func _collect_attackable_units(node: Node):
	for child in node.get_children():
		if child is EnemyUnit and child.can_attack:
			attackable_units.append(child)
		else:
			# 递归搜索子节点（如 MaskClipper）
			_collect_attackable_units(child)


## 查找玩家目标
func _find_player_target() -> void:
	if not get_tree():
		return
	var players = get_tree().get_nodes_in_group("player")
	if not players.is_empty():
		player_target = players[0]


## 更新攻击系统 - 支持多单元独立攻击
func _update_attack_system(delta: float) -> void:
	if not player_target or not is_instance_valid(player_target):
		_find_player_target()
		return

	if attackable_units.is_empty():
		return

	# 更新每个单元的攻击状态
	for unit in attackable_units:
		if not is_instance_valid(unit):
			continue

		var attack_state = unit_attack_states.get(unit)
		if not attack_state:
			continue

		# 更新单元的攻击计时器
		if attack_state.update_timer(delta):
			# 检查玩家是否在攻击范围内
			var unit_world_pos: Vector2 = _get_unit_world_position(unit)
			if attack_state.can_attack_player(player_target.global_position, unit_world_pos):
				var direction = (player_target.global_position - unit_world_pos).normalized()
				unit.attack(unit_world_pos, direction)

			# 重置该单元的攻击计时器
			attack_state.reset_attack_timer()


## 获取敌人单元在世界坐标系中的位置
func _get_unit_world_position(unit: EnemyUnit) -> Vector2:
	if not unit or not enemy_vis:
		return global_position

	# 场景树路径：EnemyUnit -> MaskClipper -> EnemyVis -> SubViewport -> Enemy
	return global_position-subviewport.size/2.0+unit.global_position
