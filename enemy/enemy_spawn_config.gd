class_name EnemySpawnConfig
extends Resource

@export var name: String = "Default Enemy"
## 敌人单元配置数组
@export var enemy_unit_configs: Array[EnemyUnitConfig] = []
@export var min_cells: int = 1
@export var max_cells: int = 1
@export var weight: int = 10


## 根据权重随机选择一个单元配置
func get_random_unit_config() -> EnemyUnitConfig:
	if enemy_unit_configs.is_empty():
		return null

	# 计算总权重
	var total_weight: int = 0
	for config in enemy_unit_configs:
		if config:
			total_weight += config.weight

	if total_weight == 0:
		return enemy_unit_configs[0]

	# 按权重随机选择
	var rand_val = randi() % total_weight
	var cumulative_weight: int = 0

	for config in enemy_unit_configs:
		if config:
			cumulative_weight += config.weight
			if rand_val < cumulative_weight:
				return config

	return enemy_unit_configs[-1]
