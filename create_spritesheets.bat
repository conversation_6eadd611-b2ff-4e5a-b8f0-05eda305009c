@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: ============================================================================
:: create_spritesheets.bat
::
:: 描述:
::   本脚本会自动检测指定文件夹中的不同动画序列 (基于文件名模式)，
::   并使用 ImageMagick 将每个序列合并成一个水平排列的雪碧图。
::
:: 文件名约定:
::   脚本期望 PNG 文件遵循以下命名模式:
::   [前缀]-[动作]_[帧编号].png
::   例如: spine_101_01-attack_00.png, my_character-idle_01.png
::
:: 使用方法:
::   create_spritesheets.bat "C:\path\to\your\image\frames"
::
:: 需求:
::   - 必须安装 ImageMagick.
:: ============================================================================

:: --- 配置 ---
:: 在此处设置您的 ImageMagick convert.exe 的完整路径。
set "IMAGEMAGICK_CONVERT=E:\software\ImageMagick-6.2.7-Q16\convert.exe"


:: --- 脚本 ---

:: 检查是否提供了输入目录参数
if "%~1"=="" (
    echo 错误: 未指定输入目录。
    echo.
    echo 用法: %~n0 "C:\path\to\your\image\frames"
    goto :eof
)

:: 检查指定目录是否存在
if not exist "%~1" (
    echo 错误: 找不到目录 - "%~1"
    goto :eof
)

:: 检查 ImageMagick convert.exe 是否存在
if not exist "%IMAGEMAGICK_CONVERT%" (
    echo 错误: 在指定路径下找不到 ImageMagick convert.exe。
    echo 请编辑此脚本并为 IMAGEMAGICK_CONVERT 设置正确的路径。
    echo 当前设置的路径为: "%IMAGEMAGICK_CONVERT%"
    goto :eof
)

echo 正在处理目录: %~1
cd /d "%~1"

:: 用于存储唯一动作名称的临时文件
set "ACTIONS_FILE=%TEMP%\unique_actions_%RANDOM%.txt"
if exist "%ACTIONS_FILE%" del "%ACTIONS_FILE%"

echo.
echo 正在查找唯一的动画动作...

:: 遍历所有符合预期模式的 PNG 文件
for %%F in (*-*.png) do (
    :: 提取动作名称 (连字符-和下划线_之间的部分)
    for /f "tokens=2 delims=-" %%A in ("%%~nF") do (
        for /f "tokens=1 delims=_" %%B in ("%%A") do (
            :: 检查此动作名称是否已经被找到
            findstr /X /C:"%%B" "%ACTIONS_FILE%" >nul
            if errorlevel 1 (
                echo 找到动作: %%B
                echo %%B>>"%ACTIONS_FILE%"
            )
        )
    )
)

if not exist "%ACTIONS_FILE%" (
    echo 没有找到符合 '前缀-动作_编号.png' 模式的动作。
    goto :eof
)

echo.
echo 正在创建雪碧图...

:: 处理找到的每个唯一动作
for /f "delims=" %%A in ('type "%ACTIONS_FILE%"') do (
    echo   - 正在处理 "%%A"...
    "%IMAGEMAGICK_CONVERT%" "*-%%A_*.png" +append "%%A.png" >nul 2>&1
    echo     -> 已创建 %%A.png
)


:: --- 清理 ---
if exist "%ACTIONS_FILE%" del "%ACTIONS_FILE%"

echo.
echo 完成。
goto :eof 