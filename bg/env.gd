extends Resource
class_name EnvironmentTheme

@export var name: String = "Default Theme"

@export_group("Background")
@export var background_scene: PackedScene

@export_group("Enemy Shape Textures")
@export var top_texture: Texture2D
@export var side_texture: Texture2D
@export var front_texture: Texture2D
@export var inner_texture: Texture2D

@export_group("Enemy Units")
@export var enemy_spawn_configs: Array[EnemySpawnConfig] = [] 