[gd_resource type="Resource" script_class="EnvironmentTheme" load_steps=13 format=3 uid="uid://b4tu6qj27tbdi"]

[ext_resource type="PackedScene" uid="uid://c8xb1318glvbm" path="res://bg/rock_bg.tscn" id="1_wyr0i"]
[ext_resource type="Script" uid="uid://deqk4g71b7w1m" path="res://enemy/enemy_spawn_config.gd" id="2_lo85t"]
[ext_resource type="Script" uid="uid://cowdme42nvfx3" path="res://enemy/enemy_unit_config.gd" id="3_dka0r"]
[ext_resource type="Texture2D" uid="uid://npe7tklubw7k" path="res://assets/imgs/texture/T_2_2_Wood_basecolor.png" id="4_dka0r"]
[ext_resource type="PackedScene" uid="uid://b5yhb4ta5ddfx" path="res://enemy/prefab/level1/level_1_enemy_1.tscn" id="4_lo85t"]
[ext_resource type="Script" uid="uid://be7w77ylk15vo" path="res://bg/env.gd" id="5_wohhm"]

[sub_resource type="Resource" id="Resource_eg4os"]
script = ExtResource("3_dka0r")
weight = 5
unit_scene = ExtResource("4_lo85t")
metadata/_custom_type_script = "uid://cowdme42nvfx3"

[sub_resource type="Resource" id="Resource_06y31"]
script = ExtResource("2_lo85t")
name = "Small Enemy"
enemy_unit_configs = Array[ExtResource("3_dka0r")]([SubResource("Resource_eg4os")])
min_cells = 1
max_cells = 1
weight = 10
metadata/_custom_type_script = "uid://deqk4g71b7w1m"

[sub_resource type="Resource" id="Resource_dka0r"]
script = ExtResource("3_dka0r")
weight = 5
unit_scene = ExtResource("4_lo85t")
metadata/_custom_type_script = "uid://cowdme42nvfx3"

[sub_resource type="Resource" id="Resource_i7g1q"]
script = ExtResource("2_lo85t")
name = "Middle Enemy"
enemy_unit_configs = Array[ExtResource("3_dka0r")]([SubResource("Resource_dka0r")])
min_cells = 2
max_cells = 2
weight = 2
metadata/_custom_type_script = "uid://deqk4g71b7w1m"

[sub_resource type="Resource" id="Resource_wohhm"]
script = ExtResource("3_dka0r")
weight = 5
unit_scene = ExtResource("4_lo85t")
metadata/_custom_type_script = "uid://cowdme42nvfx3"

[sub_resource type="Resource" id="Resource_wyr0i"]
script = ExtResource("2_lo85t")
name = "Big Enemy"
enemy_unit_configs = Array[ExtResource("3_dka0r")]([SubResource("Resource_wohhm")])
min_cells = 3
max_cells = 4
weight = 1
metadata/_custom_type_script = "uid://deqk4g71b7w1m"

[resource]
resource_local_to_scene = true
script = ExtResource("5_wohhm")
name = "Rock Theme"
background_scene = ExtResource("1_wyr0i")
top_texture = ExtResource("4_dka0r")
side_texture = ExtResource("4_dka0r")
front_texture = ExtResource("4_dka0r")
enemy_spawn_configs = Array[ExtResource("2_lo85t")]([SubResource("Resource_06y31"), SubResource("Resource_i7g1q"), SubResource("Resource_wyr0i")])
metadata/_custom_type_script = "uid://be7w77ylk15vo"
