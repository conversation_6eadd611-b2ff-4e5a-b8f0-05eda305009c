[gd_scene load_steps=12 format=3 uid="uid://c8xb1318glvbm"]

[ext_resource type="Script" uid="uid://b46ied1spwtg1" path="res://bg/bg_control.gd" id="1_eu5ah"]
[ext_resource type="Shader" uid="uid://bafyjkv4v80ia" path="res://assets/materials/shader/blend_texture.gdshader" id="2_eu5ah"]
[ext_resource type="Texture2D" uid="uid://tiaym7b11dxe" path="res://assets/imgs/texture/T_1_1_Rock_normal.png" id="3_2pc4a"]
[ext_resource type="Texture2D" uid="uid://c773gnvmdjwah" path="res://assets/imgs/texture/T_1_1_Rock_basecolor.png" id="3_6gnlk"]
[ext_resource type="Texture2D" uid="uid://jltb7r3kvv7a" path="res://assets/imgs/texture/T_1_5_Rock_normal.png" id="4_85v11"]
[ext_resource type="Texture2D" uid="uid://ch6t25p8bb3ae" path="res://assets/imgs/texture/T_1_5_Rock_basecolor.png" id="4_t1rka"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_stlxx"]
noise_type = 0
frequency = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_n67k0"]
seamless = true
noise = SubResource("FastNoiseLite_stlxx")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_2pc4a"]
noise_type = 2
frequency = 0.0057

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_85v11"]
seamless = true
noise = SubResource("FastNoiseLite_2pc4a")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_lklcp"]
shader = ExtResource("2_eu5ah")
shader_parameter/texture_1 = ExtResource("3_6gnlk")
shader_parameter/texture_2 = ExtResource("4_t1rka")
shader_parameter/normal_map_1 = ExtResource("3_2pc4a")
shader_parameter/normal_map_2 = ExtResource("4_85v11")
shader_parameter/noise_texture = SubResource("NoiseTexture2D_85v11")
shader_parameter/use_normal_mapping = true
shader_parameter/transition_depth = 1.0
shader_parameter/uv_scale_1 = Vector2(4, 9)
shader_parameter/uv_scale_2 = Vector2(3, 10)
shader_parameter/black_threshold = 0.375
shader_parameter/white_threshold = 0.0
shader_parameter/scroll_speed = Vector2(0, -0.005)
shader_parameter/anti_tile_mode = 1
shader_parameter/no_tile_noise = SubResource("NoiseTexture2D_n67k0")
shader_parameter/no_tile_noise_freq = 0.005
shader_parameter/no_tile_offset_strength_1 = 0.5
shader_parameter/no_tile_offset_strength_2 = 0.5

[node name="BGNormal1" type="Node2D"]
z_index = -10
script = ExtResource("1_eu5ah")

[node name="MapDirectionalLight2D" type="DirectionalLight2D" parent="."]
energy = 1.5
range_layer_min = -100
range_layer_max = -100
shadow_color = Color(0.145098, 0.145098, 0.145098, 0.568627)
height = 0.12

[node name="ObjDirectionalLight2D" type="DirectionalLight2D" parent="."]
color = Color(0.431373, 0.388235, 0.376471, 1)
energy = 1.5

[node name="CanvasLayer" type="CanvasLayer" parent="."]
layer = -100

[node name="ColorRect" type="ColorRect" parent="CanvasLayer"]
texture_repeat = 2
material = SubResource("ShaderMaterial_lklcp")
offset_right = 720.0
offset_bottom = 2087.0
pivot_offset = Vector2(360, 640)

[node name="CanvasModulate" type="CanvasModulate" parent="CanvasLayer"]
color = Color(0.77084, 0.770839, 0.770839, 1)
