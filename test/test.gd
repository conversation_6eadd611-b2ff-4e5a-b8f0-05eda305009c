extends Node2D


@onready var button: Button = $Button

@export var enemy_scene: PackedScene

func _ready() -> void:
	var enemy = enemy_scene.instantiate()
	enemy.global_position = Vector2(340, 340)
	add_child(enemy)
	enemy.generate_enemy_vis()
	button.pressed.connect(
		func():
			if not has_node("Enemy"):
				return
			var enemy_node:EnemyBase = get_node("Enemy")
			enemy_node._collect_attackable_units(enemy_node)
			for unit in enemy_node.attackable_units:
				var pos: Vector2 = enemy_node._get_unit_world_position(unit)
				unit.attack(pos,Vector2.DOWN)

		# await get_tree().create_timer(1).timeout
		# var enemy_new = enemy_scene.instantiate()
		# enemy_new.name = "Enemy"
		# enemy_new.global_position = Vector2(340, 340)
		# add_child(enemy_new)
		# enemy_new.generate_enemy_vis()
	)
