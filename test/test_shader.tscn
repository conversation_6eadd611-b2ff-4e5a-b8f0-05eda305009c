[gd_scene load_steps=7 format=3 uid="uid://btu5ej0tbab0y"]

[ext_resource type="Theme" uid="uid://cxbdqv0cxcy1m" path="res://addons/simple-gui-transitions/example/theme.tres" id="1_k5ceh"]
[ext_resource type="Material" uid="uid://dsr3b1c33apxb" path="res://addons/simple-gui-transitions/materials/transform.tres" id="1_lxbaq"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="2_k5ceh"]

[sub_resource type="ViewportTexture" id="ViewportTexture_k5ceh"]
viewport_path = NodePath("Control/VBoxContainer/TextureRect/SubViewport")

[sub_resource type="Gradient" id="Gradient_81crv"]

[sub_resource type="GradientTexture1D" id="GradientTexture1D_pnnlv"]
gradient = SubResource("Gradient_81crv")

[node name="TestShader" type="Node2D"]

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0
theme = ExtResource("1_k5ceh")

[node name="VBoxContainer" type="VBoxContainer" parent="Control"]
layout_mode = 0
offset_right = 202.0
offset_bottom = 85.0
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="SubViewportContainer" type="SubViewportContainer" parent="Control/VBoxContainer"]
visible = false
material = ExtResource("1_lxbaq")
layout_mode = 2

[node name="SubViewport" type="SubViewport" parent="Control/VBoxContainer/SubViewportContainer"]
handle_input_locally = false
size = Vector2i(972, 1647)
render_target_update_mode = 0

[node name="Button" type="Button" parent="Control/VBoxContainer/SubViewportContainer/SubViewport"]
offset_right = 202.0
offset_bottom = 37.0
text = "去我饿"

[node name="Sprite2D" type="Sprite2D" parent="Control/VBoxContainer/SubViewportContainer/SubViewport/Button"]
position = Vector2(-24, -37)
texture = ExtResource("2_k5ceh")

[node name="TextureRect" type="TextureRect" parent="Control/VBoxContainer"]
layout_mode = 2
texture = SubResource("ViewportTexture_k5ceh")

[node name="SubViewport" type="SubViewport" parent="Control/VBoxContainer/TextureRect"]
handle_input_locally = false
size = Vector2i(362, 170)
render_target_update_mode = 4

[node name="Button" type="Button" parent="Control/VBoxContainer/TextureRect/SubViewport"]
offset_left = 100.53
offset_top = 112.79
offset_right = 194.53
offset_bottom = 160.79
text = "去我饿"

[node name="Sprite2D" type="Sprite2D" parent="Control/VBoxContainer/TextureRect/SubViewport/Button"]
position = Vector2(-24, -37)
texture = ExtResource("2_k5ceh")

[node name="TextureRect2" type="TextureRect" parent="Control/VBoxContainer"]
custom_minimum_size = Vector2(0, 100)
layout_mode = 2
texture = SubResource("GradientTexture1D_pnnlv")

[node name="SubViewportContainer2" type="SubViewportContainer" parent="Control"]
visible = false
material = ExtResource("1_lxbaq")
layout_mode = 0

[node name="SubViewport" type="SubViewport" parent="Control/SubViewportContainer2"]
handle_input_locally = false
size = Vector2i(202, 85)
render_target_update_mode = 0

[node name="Button" type="Button" parent="Control/SubViewportContainer2/SubViewport"]
offset_left = 3.0
offset_right = 97.0
offset_bottom = 48.0
text = "去我饿"

[node name="Sprite2D" type="Sprite2D" parent="Control/SubViewportContainer2/SubViewport/Button"]
position = Vector2(-24, -37)
texture = ExtResource("2_k5ceh")
