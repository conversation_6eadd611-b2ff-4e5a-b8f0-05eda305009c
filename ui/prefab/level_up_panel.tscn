[gd_scene load_steps=6 format=3 uid="uid://bvfj6pi6divbx"]

[ext_resource type="Material" uid="uid://d3juvi1g0wppy" path="res://assets/materials/effect/frosted_glass.tres" id="1_0vt1b"]
[ext_resource type="Theme" uid="uid://bmf31mho1mjjt" path="res://ui/sprout_lands_ui/sprout_lands_theme.tres" id="2_theme"]
[ext_resource type="Script" uid="uid://dixhgbc8y2jw4" path="res://ui/level_up_panel.gd" id="3_script"]
[ext_resource type="Script" uid="uid://bimelmc1ce1b2" path="res://addons/simple-gui-transitions/transition.gd" id="3_ydg34"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_oyv7u"]

[node name="LevelUpPanel" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")
script = ExtResource("3_script")

[node name="GuiTransition" type="Node" parent="."]
script = ExtResource("3_ydg34")
auto_start = 0
animation_enter = 0
animation_leave = 5
duration = 2.0
delay = 0.0
layout = NodePath("..")
controls = Array[NodePath]([NodePath("../MainPanel")])
metadata/_custom_type_script = "uid://bimelmc1ce1b2"

[node name="BlurMask" type="ColorRect" parent="."]
material = ExtResource("1_0vt1b")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
color = Color(0, 0, 0, 0.7)

[node name="MainPanel" type="PanelContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -400.0
offset_right = 300.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxEmpty_oyv7u")

[node name="Panel" type="Panel" parent="MainPanel"]
self_modulate = Color(0.694118, 0.694118, 0.694118, 0.901961)
layout_mode = 2

[node name="MainContainer" type="MarginContainer" parent="MainPanel"]
layout_mode = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 20
theme_override_constants/margin_right = 20
theme_override_constants/margin_bottom = 20

[node name="VBoxContainer" type="VBoxContainer" parent="MainPanel/MainContainer"]
layout_mode = 2
theme_override_constants/separation = 10

[node name="TitleSection" type="VBoxContainer" parent="MainPanel/MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_constants/separation = 5

[node name="TitleLabel" type="Label" parent="MainPanel/MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(1, 0.894, 0.71, 1)
theme_override_font_sizes/font_size = 32
text = "🎉 升级！"
horizontal_alignment = 1

[node name="LevelLabel" type="Label" parent="MainPanel/MainContainer/VBoxContainer/TitleSection"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_color = Color(0.941, 0.902, 0.549, 1)
theme_override_font_sizes/font_size = 18
text = "等级 5 → 6"
horizontal_alignment = 1

[node name="OwnedItemsSection" type="VBoxContainer" parent="MainPanel/MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 10

[node name="BallsSection" type="VBoxContainer" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 5

[node name="BallsTitle" type="Label" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/BallsSection"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 0.894, 0.71, 1)
theme_override_font_sizes/font_size = 24
text = "🏀 拥有弹球 (3/10)"

[node name="BallsGridContainer" type="HBoxContainer" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/BallsSection"]
layout_mode = 2
size_flags_vertical = 3
alignment = 1

[node name="BallsGrid" type="GridContainer" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/BallsSection/BallsGridContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 28
theme_override_constants/v_separation = 10
columns = 5

[node name="RelicsSection" type="VBoxContainer" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 5

[node name="RelicsTitle" type="Label" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/RelicsSection"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 0.894, 0.71, 1)
theme_override_font_sizes/font_size = 24
text = "💎 拥有遗物 (2/10)"

[node name="RelicsGridContainer" type="HBoxContainer" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/RelicsSection"]
layout_mode = 2
size_flags_vertical = 3
alignment = 1

[node name="RelicsGrid" type="GridContainer" parent="MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/RelicsSection/RelicsGridContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/h_separation = 28
theme_override_constants/v_separation = 10
columns = 5

[node name="UpgradeOptionsSection" type="VBoxContainer" parent="MainPanel/MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 2
size_flags_stretch_ratio = 0.5
theme_override_constants/separation = 5

[node name="UpgradeOptionsTitle" type="Label" parent="MainPanel/MainContainer/VBoxContainer/UpgradeOptionsSection"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 0.894, 0.71, 1)
theme_override_font_sizes/font_size = 24
text = "⭐ 选择升级 (3选1)"

[node name="UpgradeOptionsGridContainer" type="HBoxContainer" parent="MainPanel/MainContainer/VBoxContainer/UpgradeOptionsSection"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 5
alignment = 1

[node name="UpgradeOptionsGrid" type="GridContainer" parent="MainPanel/MainContainer/VBoxContainer/UpgradeOptionsSection/UpgradeOptionsGridContainer"]
layout_mode = 2
size_flags_horizontal = 0
size_flags_vertical = 0
theme_override_constants/h_separation = 15
theme_override_constants/v_separation = 10
columns = 3

[node name="EffectDescriptionSection" type="PanelContainer" parent="MainPanel/MainContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 160)
layout_mode = 2
theme_type_variation = &"EffectDescription"

[node name="EffectDescriptionMargin" type="MarginContainer" parent="MainPanel/MainContainer/VBoxContainer/EffectDescriptionSection"]
layout_mode = 2
theme_override_constants/margin_left = 10
theme_override_constants/margin_top = 5
theme_override_constants/margin_right = 10
theme_override_constants/margin_bottom = 5

[node name="EffectDescriptionLabel" type="RichTextLabel" parent="MainPanel/MainContainer/VBoxContainer/EffectDescriptionSection/EffectDescriptionMargin"]
layout_mode = 2
theme_override_colors/default_color = Color(0.941, 0.969, 1, 1)
theme_override_font_sizes/normal_font_size = 20
bbcode_enabled = true
text = "[center][color=#999999][i]点击物品或升级选项查看详细说明[/i][/color][/center]"
fit_content = true

[node name="ConfirmSection" type="VBoxContainer" parent="MainPanel/MainContainer/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0
theme_override_constants/separation = 5

[node name="ConfirmButton" type="Button" parent="MainPanel/MainContainer/VBoxContainer/ConfirmSection"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
size_flags_horizontal = 4
theme_override_colors/font_disabled_color = Color(0.364711, 0.364711, 0.364711, 1)
disabled = true
text = "请先选择一个升级选项"
