[gd_scene load_steps=3 format=3 uid="uid://c8h4vn2lal5xt"]

[ext_resource type="Theme" uid="uid://bmf31mho1mjjt" path="res://ui/sprout_lands_ui/sprout_lands_theme.tres" id="1_theme"]
[ext_resource type="Script" path="res://ui/upgrade_option.gd" id="2_script"]

[node name="UpgradeOption" type="Button"]
custom_minimum_size = Vector2(150, 80)
layout_mode = 2
theme = ExtResource("1_theme")
flat = false
script = ExtResource("2_script")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 5
alignment = 1

[node name="IconLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "⚡"
horizontal_alignment = 1

[node name="NameLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 12
text = "雷电弹球"
horizontal_alignment = 1
autowrap_mode = 2
