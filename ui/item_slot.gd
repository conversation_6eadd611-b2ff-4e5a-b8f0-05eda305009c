extends Button
class_name ItemSlot

## 物品槽UI组件
##
## 用于显示弹球或遗物的槽位，支持：
## - 显示物品图标
## - 空槽状态显示
## - 点击交互
## - 视觉反馈

# 信号定义
signal item_clicked(item_data: Dictionary)

# UI节点引用
@onready var icon_label: Label = $IconLabel

# 数据存储
var item_data: Dictionary = {}
var is_empty: bool = true

# 样式常量
const EMPTY_COLOR = Color(0.5, 0.5, 0.5, 0.8)
const FILLED_COLOR = Color.WHITE
const HOVER_COLOR = Color(1.2, 1.2, 1.0)

func _ready() -> void:

	# 设置初始状态
	set_empty()

	# 设置暂停模式以确保在游戏暂停时仍能接收输入
	process_mode = Node.PROCESS_MODE_WHEN_PAUSED

	print("物品槽初始化完成")
	print("  mouse_filter:", mouse_filter)
	print("  disabled:", disabled)
	print("  size:", size)
	print("  process_mode:", process_mode)


## 设置物品数据
## @param data: 物品数据字典，包含name、icon、description等信息
func set_item_data(data: Dictionary) -> void:
	item_data = data
	is_empty = false

	# 更新显示
	var item_icon = data.get("icon", "❓")
	icon_label.text = item_icon

	# 更新按钮状态
	disabled = false
	modulate = FILLED_COLOR

	# 设置提示文本
	tooltip_text = data.get("name", "未知物品")

	print("设置物品槽数据：", data.get("name", "未知物品"))
	print("  物品槽状态：disabled =", disabled, ", size =", size, ", custom_minimum_size =", custom_minimum_size)
	print("  全局位置：", global_position, ", 局部位置：", position)
	print("  可点击区域：", get_rect())

## 设置为空槽状态
func set_empty() -> void:
	item_data = {}
	is_empty = true

	# 更新显示
	icon_label.text = ""

	# 更新按钮状态
	disabled = true
	modulate = EMPTY_COLOR
	tooltip_text = ""

## 获取物品数据
func get_item_data() -> Dictionary:
	return item_data

## 检查是否为空槽
func is_slot_empty() -> bool:
	return is_empty

## 槽位点击处理
func _on_slot_pressed() -> void:
	print("=== 物品槽点击事件触发 ===")
	print("is_empty:", is_empty)
	print("disabled:", disabled)
	print("item_data:", item_data)

	if not is_empty:
		print("物品槽被点击：", item_data.get("name", "未知物品"))
		print("发射item_clicked信号")
		item_clicked.emit(item_data)
	else:
		print("点击了空槽位")
	print("==========================")
