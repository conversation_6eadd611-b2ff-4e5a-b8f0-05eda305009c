extends Control
class_name LayerBar

@onready var top_bar: TextureProgressBar = $Top
@onready var bottom_bar: ProgressBar = $Bottom
@onready var mid_bar: ProgressBar = $Mid
@onready var mid_timer: Timer = $MidTimer
@onready var bottom_timer: Timer = $BottomTimer


var value = 100.0: set = _set_value

func _set_value(new_value: float):
	var prev_value = value
	value = new_value
	
	if new_value <= prev_value:
		bottom_bar.visible = true
		top_bar.value = new_value
		top_bar.material.set_shader_parameter("EdgeWaveLevel", new_value)
		mid_bar.value = new_value
		bottom_timer.start()
	elif new_value > prev_value:
		mid_bar.visible = true
		mid_bar.value = new_value
		mid_timer.start()


func _on_mid_timer_timeout():
	create_tween().tween_property(top_bar, "value", value, 0.2).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN).finished.connect(func():
		bottom_bar.value = value
		mid_bar.visible = false
		bottom_bar.visible = false
	)
	create_tween().tween_property(top_bar.material, "shader_parameter/EdgeWaveLevel", value, 0.2).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN)


func _on_bottom_timer_timeout() -> void:
	create_tween().tween_property(bottom_bar, "value", value, 0.2).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN).finished.connect(func():
		mid_bar.visible = false
		bottom_bar.visible = false
	)
