[gd_scene load_steps=3 format=3 uid="uid://cgciuhuxrtwi"]

[ext_resource type="Script" uid="uid://b68y3prhmkqry" path="res://ui/level_up_panel_test.gd" id="1_test_script"]
[ext_resource type="PackedScene" uid="uid://bvfj6pi6divbx" path="res://ui/prefab/level_up_panel.tscn" id="2_level_up_panel"]

[node name="LevelUpPanelTest" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_test_script")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.3, 0.4, 1)

[node name="InfoLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -100.0
offset_right = 400.0
offset_bottom = -20.0
grow_vertical = 0
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 16
text = "升级面板测试
按键说明：
T - 显示升级面板
ESC - 关闭升级面板
R - 重新加载场景"
vertical_alignment = 1

[node name="LevelUpPanel" parent="." instance=ExtResource("2_level_up_panel")]
visible = false
layout_mode = 1
