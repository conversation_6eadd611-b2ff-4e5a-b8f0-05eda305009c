class_name PoisonAbility
extends BallAbilityBase

@export var poison_debuff_template: AttributeBuffDOT

func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	if poison_debuff_template == null:
		push_warning("poison_debuff_template is not set in PoisonAbility")
		return
	
	if target.has_method("add_buff"):
		target.add_buff(poison_debuff_template)


func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	if poison_debuff_template == null:
		push_warning("poison_debuff_template is not set in PoisonAbility")
		return
	
	if target.has_method("add_buff"):
		target.add_buff(poison_debuff_template)
