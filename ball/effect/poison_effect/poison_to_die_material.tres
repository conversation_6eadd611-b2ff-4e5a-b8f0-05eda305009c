[gd_resource type="ShaderMaterial" load_steps=4 format=3 uid="uid://bjg14of7jmmu0"]

[ext_resource type="Shader" uid="uid://ceypmokvi43y7" path="res://assets/materials/shader/dissolve.gdshader" id="1_onm3d"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_khatr"]
frequency = 0.0063
fractal_type = 2
fractal_lacunarity = 0.91
fractal_gain = 0.02

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_6s600"]
noise = SubResource("FastNoiseLite_khatr")

[resource]
resource_local_to_scene = true
shader = ExtResource("1_onm3d")
shader_parameter/debug_percentage = 1.0
shader_parameter/burn_texture = SubResource("NoiseTexture2D_6s600")
shader_parameter/layer_1 = Color(0.10748, 0.10748, 0.10748, 1)
shader_parameter/size_1 = 0.06
shader_parameter/layer_2 = Color(0.177347, 0.630258, 0.286908, 1)
shader_parameter/size_2 = 0.055
shader_parameter/layer_3 = Color(0, 1.3, 0.3, 1)
shader_parameter/size_3 = 0.07
shader_parameter/alpha_threshold = 0.0
