[gd_resource type="Resource" script_class="PoisonEffectHandler" load_steps=5 format=3 uid="uid://6d5s11tx3shh"]

[ext_resource type="Material" uid="uid://cmrqqbs5mptgt" path="res://ball/effect/poison_effect/poison_debuff_material.tres" id="1_3l88r"]
[ext_resource type="PackedScene" uid="uid://u61jb1nfuges" path="res://ball/effect/poison_effect/poison_particle.tscn" id="2_ji401"]
[ext_resource type="Script" uid="uid://hxqw37dyx52" path="res://enemy/buff_handlers/poison_effect_handler.gd" id="3_kcgpg"]
[ext_resource type="Material" uid="uid://bjg14of7jmmu0" path="res://ball/effect/poison_effect/poison_to_die_material.tres" id="3_r6m7k"]

[resource]
resource_local_to_scene = true
script = ExtResource("3_kcgpg")
posion_material = ExtResource("1_3l88r")
posion_to_die_material = ExtResource("3_r6m7k")
posion_particle = ExtResource("2_ji401")
damage_color = Color(0.498039, 1, 0.0901961, 1)
splash_radius = 300.0
splash_probability = 0.3
splash_on_dot = true
splash_on_death = true
handled_buff_name = "poison"
metadata/_custom_type_script = "uid://bfvxq0i61k3dm"
