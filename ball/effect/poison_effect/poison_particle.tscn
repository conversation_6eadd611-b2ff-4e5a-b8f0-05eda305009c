[gd_scene load_steps=9 format=3 uid="uid://u61jb1nfuges"]

[ext_resource type="Texture2D" uid="uid://vwfv2n316yx6" path="res://assets/imgs/effects/46.png" id="1_enl1m"]
[ext_resource type="Script" uid="uid://dcx66e75435fp" path="res://effect/particle_root_effect.gd" id="2_4k3ss"]

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_g67ej"]
blend_mode = 1

[sub_resource type="Curve" id="Curve_kqft1"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.111748, 0), 0.0, 0.0, 0, 0, Vector2(0.26361, 1), 0.0, 0.0, 0, 0, Vector2(0.792387, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 5

[sub_resource type="CurveTexture" id="CurveTexture_883au"]
curve = SubResource("Curve_kqft1")

[sub_resource type="Curve" id="Curve_pk21r"]
_data = [Vector2(0.50173, 0.834174), 0.0, 0.0, 0, 0, Vector2(1, 0.673815), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_6dy7x"]
curve = SubResource("Curve_pk21r")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_46n8k"]
lifetime_randomness = 0.45
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(15, 15, 0)
direction = Vector3(0, -1, 0)
spread = 20.0
initial_velocity_min = 60.0
initial_velocity_max = 80.0
gravity = Vector3(0, -20, 0)
scale_min = 0.02
scale_max = 0.04
scale_curve = SubResource("CurveTexture_6dy7x")
color = Color(0.266667, 1, 0, 1)
alpha_curve = SubResource("CurveTexture_883au")
turbulence_enabled = true
turbulence_noise_scale = 2.923
turbulence_noise_speed = Vector3(0, -1.79, 0)
turbulence_noise_speed_random = 1.1
turbulence_influence_min = 0.022

[node name="PoisonParticle" type="GPUParticles2D"]
material = SubResource("CanvasItemMaterial_g67ej")
amount = 5
texture = ExtResource("1_enl1m")
lifetime = 3.0
process_material = SubResource("ParticleProcessMaterial_46n8k")
script = ExtResource("2_4k3ss")
