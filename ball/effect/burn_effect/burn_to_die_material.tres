[gd_resource type="ShaderMaterial" load_steps=4 format=3 uid="uid://x372txrlt4xk"]

[ext_resource type="Shader" uid="uid://h8a2ktywia65" path="res://assets/materials/shader/direction_dissolve.gdshader" id="1_rasd6"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_2mqen"]
noise_type = 0
frequency = 0.0123
fractal_lacunarity = 2.76
fractal_gain = 0.625

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_k7dyv"]
noise = SubResource("FastNoiseLite_2mqen")

[resource]
shader = ExtResource("1_rasd6")
shader_parameter/noiseForce = 1.28
shader_parameter/noiseTexture = SubResource("NoiseTexture2D_k7dyv")
shader_parameter/burnColor = Color(1.10196, 0.333333, 0, 1)
shader_parameter/borderWidth = 0.35
shader_parameter/direction = 90.0
