[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://bsdji8rh2c8r5"]

[ext_resource type="Shader" uid="uid://bionbfobjqg6w" path="res://assets/materials/shader/radiation_ball.gdshader" id="1_lsd80"]

[resource]
resource_local_to_scene = true
shader = ExtResource("1_lsd80")
shader_parameter/time_scale = 3.0
shader_parameter/time_offset = 0.0
shader_parameter/core_color = Color(0.244964, 0.637949, 1.15514e-06, 1)
shader_parameter/edge_color = Color(0.4995, 1, 0.09, 1)
shader_parameter/size_scale = 1.5
shader_parameter/edge_softness = 0.0
shader_parameter/core_size = 1.369
shader_parameter/edge_size = 0.0
shader_parameter/noise_scale = 10.0
shader_parameter/noise_strength = 0.176
