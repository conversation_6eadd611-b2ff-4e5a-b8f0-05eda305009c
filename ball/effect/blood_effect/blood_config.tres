[gd_resource type="Resource" script_class="AttributeBuffDOT" load_steps=11 format=3 uid="uid://gxio2wtbnt5o"]

[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="1_ftyxo"]
[ext_resource type="Script" uid="uid://5ivjabbc5wk6" path="res://addons/attribute_manager/AttributeBuffDOT.gd" id="1_pght3"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="2_ci7ja"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="2_s0600"]

[sub_resource type="Resource" id="Resource_wkx0l"]
script = ExtResource("1_ftyxo")
base_value = 10.0
can_cache = true

[sub_resource type="Resource" id="Resource_1dess"]
script = ExtResource("1_ftyxo")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_xp312"]
script = ExtResource("1_ftyxo")
base_value = 99.0
can_cache = true

[sub_resource type="Resource" id="Resource_uv7xx"]
script = ExtResource("1_ftyxo")
base_value = 5.0
can_cache = true

[sub_resource type="Resource" id="Resource_0ygqu"]
script = ExtResource("2_s0600")
growth_per_level = 35.0
base_value = 30.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_nem48"]
resource_local_to_scene = true
script = ExtResource("2_ci7ja")
attributes = Dictionary[StringName, ExtResource("1_ftyxo")]({
&"duration": SubResource("Resource_wkx0l"),
&"level": SubResource("Resource_1dess"),
&"max_stacks": SubResource("Resource_xp312"),
&"period": SubResource("Resource_uv7xx"),
&"value": SubResource("Resource_0ygqu")
})

[resource]
resource_local_to_scene = true
script = ExtResource("1_pght3")
buff_name = "blood"
operation = 1
policy = 1
merging = 0
attribute_set = SubResource("Resource_nem48")
metadata/_custom_type_script = "uid://5ivjabbc5wk6"
