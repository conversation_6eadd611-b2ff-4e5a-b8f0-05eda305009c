[gd_resource type="ShaderMaterial" load_steps=4 format=3 uid="uid://tgj0kdb4mduw"]

[ext_resource type="Shader" uid="uid://ceypmokvi43y7" path="res://assets/materials/shader/dissolve.gdshader" id="1_ujf3n"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_lt4yx"]
frequency = 0.002
fractal_type = 3
fractal_lacunarity = 1.76
fractal_gain = 0.765

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_i1wwn"]
seamless = true
seamless_blend_skirt = 1.0
noise = SubResource("FastNoiseLite_lt4yx")

[resource]
shader = ExtResource("1_ujf3n")
shader_parameter/debug_percentage = 1.0
shader_parameter/burn_texture = SubResource("NoiseTexture2D_i1wwn")
shader_parameter/layer_1 = Color(0, 0, 0, 1)
shader_parameter/size_1 = 0.05
shader_parameter/layer_2 = Color(0.32, 0, 0.00533325, 1)
shader_parameter/size_2 = 0.07
shader_parameter/layer_3 = Color(0.61, 0, 0.0101665, 1)
shader_parameter/size_3 = 0.1
shader_parameter/alpha_threshold = 0.0
