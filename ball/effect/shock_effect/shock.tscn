[gd_scene load_steps=6 format=3 uid="uid://bdva2y2hh5phs"]

[ext_resource type="Shader" uid="uid://brv74i6h0im0h" path="res://assets/materials/shader/crack.gdshader" id="1_1jdhb"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="2_24tih"]
[ext_resource type="Script" uid="uid://dl3whws4x4r6c" path="res://effect/shock.gd" id="3_vjtf3"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_r7hq4"]
shader = ExtResource("1_1jdhb")
shader_parameter/u_scale = Vector2(3, 3)
shader_parameter/u_speed = 0.0
shader_parameter/background_color = Color(0, 0, 0, 0)
shader_parameter/crack_color = Color(1.2, 0.733, 0, 1)
shader_parameter/crack_width = 0.01
shader_parameter/edge_fade = 0.613
shader_parameter/edge_smoothness = 0.04

[sub_resource type="RectangleShape2D" id="RectangleShape2D_b88vt"]
size = Vector2(118, 118)

[node name="Shock" type="Sprite2D"]
z_index = -1
material = SubResource("ShaderMaterial_r7hq4")
scale = Vector2(2.2, 2.2)
texture = ExtResource("2_24tih")
script = ExtResource("3_vjtf3")

[node name="Timer" type="Timer" parent="."]
wait_time = 0.3
one_shot = true

[node name="Area2D" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 15

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
visible = false
shape = SubResource("RectangleShape2D_b88vt")

[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
