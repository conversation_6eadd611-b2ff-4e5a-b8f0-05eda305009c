extends Node

var id_ball_pools := {}
var balls := []


func create_ball(prefab: PackedScene) -> Node:
	var node = prefab.instantiate()
	balls.append(node)
	id_ball_pools[node.get_instance_id()] = node
	return node


func get_ball(type_id: int, direction: Vector2, pos: Vector2, parent: Node = null) -> Node:
	var node = id_ball_pools.get(type_id, null)
	if node == null:
		return null
	else:
		node.global_position = pos
		if parent:
			parent.add_child(node)
		else:
			get_tree().current_scene.add_child(node)
		node.init(direction)
		node.visible = true
		return node


func recycle_ball(ball) -> void:
	if ball == null or ball.get_parent() == null:
		return
	ball.is_active = false
	ball.get_parent().remove_child(ball)
	ball.visible = false
