[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://donaevmmjeptc"
path.s3tc="res://.godot/imported/普通球.png-1c69a40791ce7cfd7390b339c5583530.s3tc.ctex"
path.etc2="res://.godot/imported/普通球.png-1c69a40791ce7cfd7390b339c5583530.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}

[deps]

source_file="res://assets/imgs/balls/普通球.png"
dest_files=["res://.godot/imported/普通球.png-1c69a40791ce7cfd7390b339c5583530.s3tc.ctex", "res://.godot/imported/普通球.png-1c69a40791ce7cfd7390b339c5583530.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
