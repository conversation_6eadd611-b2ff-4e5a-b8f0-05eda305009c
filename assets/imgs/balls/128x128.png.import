[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://u5ndsxu34wr3"
path.s3tc="res://.godot/imported/128x128.png-a2a1ed79c771dd575b3236a5ca9d4d4b.s3tc.ctex"
path.etc2="res://.godot/imported/128x128.png-a2a1ed79c771dd575b3236a5ca9d4d4b.etc2.ctex"
metadata={
"imported_formats": ["s3tc_bptc", "etc2_astc"],
"vram_texture": true
}

[deps]

source_file="res://assets/imgs/balls/128x128.png"
dest_files=["res://.godot/imported/128x128.png-a2a1ed79c771dd575b3236a5ca9d4d4b.s3tc.ctex", "res://.godot/imported/128x128.png-a2a1ed79c771dd575b3236a5ca9d4d4b.etc2.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
