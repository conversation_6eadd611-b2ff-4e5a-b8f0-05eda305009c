shader_type canvas_item;

// 流体效果着色器 (Fluid Effect Shader)
// 使用噪声贴图创建流动的流体效果

// 噪声贴图
uniform sampler2D noise1 : repeat_enable; // 第一层噪声贴图
uniform sampler2D noise2 : repeat_enable; // 第二层噪声贴图

// 滚动速度
uniform vec2 scroll1 = vec2(0.15, 0.25);   // 第一层噪声滚动速度
uniform vec2 scroll2 = vec2(-0.15, -0.25); // 第二层噪声滚动速度（反方向）

// 第二层噪声的缩放
uniform float tex2_scale : hint_range(0.0, 10.0, 0.1) = 1.0;

// 颜色渐变贴图
uniform sampler2D overlap_color_gradient;  // 强度颜色渐变

// 效果参数
uniform float overlap_factor : hint_range(0.0, 10.0, 0.1) = 3.0;  // 重叠因子
uniform float color_factor : hint_range(0.0, 10.0, 0.1) = 1.0;    // 颜色强度因子
uniform float blur : hint_range(0.0, 10.0, 0.1) = 1.0;            // 模糊强度
uniform float delay_v : hint_range(0.0, 10.0, 0.1) = 1.0;         // 延迟值
instance uniform float overall_alpha : hint_range(0.0, 1.0) = 1.0;

// 延迟类型
// 0: 水平延迟 (X轴)
// 1: 垂直延迟 (Y轴)
// 2: 径向延迟 (从中心)
uniform int delay_type : hint_range(0, 5) = 0;

// 是否嵌入原始纹理的alpha
uniform bool embed = true;

// 边缘效果参数
uniform float edge_threshold : hint_range(0.0, 1.0, 0.01) = 0.4;  // 边缘阈值
uniform float edge_softness : hint_range(0.0, 1.0, 0.01) = 0.1;   // 边缘软化程度
uniform float edge_noise_scale : hint_range(0.1, 10.0, 0.1) = 3.0; // 边缘噪声缩放
uniform float edge_noise_influence : hint_range(0.0, 1.0, 0.01) = 0.5; // 噪声对边缘的影响程度
uniform vec2 edge_noise_scroll = vec2(0.05, 0.03); // 边缘噪声滚动速度

// 边缘方向控制
uniform int edge_direction_mode : hint_range(0, 3, 1) = 0; // 边缘方向模式: 0=左, 1=右, 2=上, 3=下
uniform bool use_multiple_edges = false; // 是否使用多个方向的边缘
uniform bool edge_left = true;   // 左边缘
uniform bool edge_right = false; // 右边缘
uniform bool edge_top = false;   // 上边缘
uniform bool edge_bottom = false; // 下边缘
uniform bool edge_radial = false; // 径向边缘（从中心向外）
uniform float edge_animation_speed = 1.0; // 边缘动画速度

void vertex() {
	// 顶点着色器（当前未使用）
}

void fragment() {

	// 调整时间
	float adjust_time = TIME;

	// 获取原始纹理的alpha通道
	float tex_alpha = 1.0;
	if (embed) {
		tex_alpha = textureLod(TEXTURE, UV, blur * 1.0).a;
	}

	// 采样两个噪声贴图并创建流动效果
	float intensity_1 = texture(noise1, UV + adjust_time * scroll1).r;
	float intensity_2 = texture(noise2, tex2_scale * (UV + adjust_time * scroll2)).r;

	// 根据选择的延迟类型计算延迟值
	float delay_value = 0.0;
	if (delay_type == 0) {
		delay_value = (1.0 - UV.x) * delay_v; // 水平延迟
	}
	if (delay_type == 1) {
		delay_value = (1.0 - UV.y) * delay_v; // 垂直延迟
	}
	if (delay_type == 2) {
		delay_value = length(UV - 0.5) * delay_v; // 径向延迟
	}

	// 计算最终强度值
	float intensity = tex_alpha * color_factor * (intensity_1 * intensity_2 + 0.1) - delay_value;

	// 使用强度值从颜色渐变中采样
	vec4 overlap_color = texture(overlap_color_gradient, vec2(intensity * overlap_factor));

	// 创建不规则边缘效果
	// 生成边缘噪声
	float edge_noise = texture(noise1, UV * edge_noise_scale + adjust_time * edge_animation_speed * edge_noise_scroll).r;

	// 计算边缘透明度
	float edge_alpha = 1.0;

	if (use_multiple_edges) {
		// 使用多个方向的边缘
		float left_edge = 0.0;
		float right_edge = 0.0;
		float top_edge = 0.0;
		float bottom_edge = 0.0;
		float radial_edge = 0.0;

		// 计算各个方向的边缘
		if (edge_left) {
			float dist = UV.x; // 左边缘距离
			float noisy_edge = dist + (edge_noise - 0.5) * edge_noise_influence;
			left_edge = smoothstep(edge_threshold - edge_softness, edge_threshold + edge_softness, noisy_edge);
		} else {
			left_edge = 1.0;
		}

		if (edge_right) {
			float dist = 1.0 - UV.x; // 右边缘距离
			float noisy_edge = dist + (edge_noise - 0.5) * edge_noise_influence;
			right_edge = smoothstep(edge_threshold - edge_softness, edge_threshold + edge_softness, noisy_edge);
		} else {
			right_edge = 1.0;
		}

		if (edge_top) {
			float dist = UV.y; // 上边缘距离
			float noisy_edge = dist + (edge_noise - 0.5) * edge_noise_influence;
			top_edge = smoothstep(edge_threshold - edge_softness, edge_threshold + edge_softness, noisy_edge);
		} else {
			top_edge = 1.0;
		}

		if (edge_bottom) {
			float dist = 1.0 - UV.y; // 下边缘距离
			float noisy_edge = dist + (edge_noise - 0.5) * edge_noise_influence;
			bottom_edge = smoothstep(edge_threshold - edge_softness, edge_threshold + edge_softness, noisy_edge);
		} else {
			bottom_edge = 1.0;
		}

		if (edge_radial) {
			float dist = 1.0 - length(UV - vec2(0.5)); // 径向边缘距离
			float noisy_edge = dist + (edge_noise - 0.5) * edge_noise_influence;
			radial_edge = smoothstep(edge_threshold - edge_softness, edge_threshold + edge_softness, noisy_edge);
		} else {
			radial_edge = 1.0;
		}

		// 组合所有边缘效果（取最小值，即所有边缘的交集）
		edge_alpha = min(min(min(min(left_edge, right_edge), top_edge), bottom_edge), radial_edge);
	} else {
		// 使用单一方向的边缘（基于edge_direction_mode）
		float distance_to_edge;

		if (edge_direction_mode == 0) {
			distance_to_edge = UV.x; // 左边缘
		} else if (edge_direction_mode == 1) {
			distance_to_edge = 1.0 - UV.x; // 右边缘
		} else if (edge_direction_mode == 2) {
			distance_to_edge = UV.y; // 上边缘
		} else if (edge_direction_mode == 3) {
			distance_to_edge = 1.0 - UV.y; // 下边缘
		} else {
			distance_to_edge = 1.0 - length(UV - vec2(0.5)); // 径向边缘
		}

		// 将噪声应用到边缘距离
		float noisy_edge = distance_to_edge + (edge_noise - 0.5) * edge_noise_influence;

		// 使用平滑步进创建柔和的边缘过渡
		edge_alpha = smoothstep(edge_threshold - edge_softness, edge_threshold + edge_softness, noisy_edge);
	}

	// 应用边缘透明度到最终颜色
	COLOR = overlap_color;
	COLOR.a *= edge_alpha * overall_alpha;
}