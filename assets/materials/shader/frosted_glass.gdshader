/*
    毛玻璃效果 Shader（扭曲 + 高斯模糊）
    基于 Shadertoy 代码转换为 Godot 4.4 兼容格式

    功能：
    - 模式 0：静态毛玻璃扭曲效果
    - 模式 1：高斯模糊效果
    - 支持透明度调节
    - 支持效果强度控制
*/

shader_type canvas_item;

// ========== 效果模式选择 ==========
/**
 * 效果模式
 * 0 = 毛玻璃扭曲效果
 * 1 = 高斯模糊效果
 */
uniform int effect_mode : hint_range(0, 1) = 0;

// ========== 基础参数 ==========
/**
 * 整体透明度
 * 控制毛玻璃效果的整体透明程度
 */
uniform float alpha : hint_range(0.0, 1.0) = 0.8;

/**
 * 扭曲强度
 * 控制毛玻璃扭曲效果的强度
 */
uniform float distortion_strength : hint_range(0.0, 2.0) = 1.0;

/**
 * 噪声扭曲强度
 * 控制噪声纹理对扭曲的影响程度
 */
uniform float noise_distortion : hint_range(0.0, 0.2) = 0.05;

/**
 * 噪声缩放
 * 控制噪声纹理的缩放程度，影响扭曲的细节密度
 * 仅在毛玻璃扭曲模式下使用
 */
uniform float noise_scale : hint_range(0.1, 10.0) = 1.0;

// ========== 高斯模糊参数 ==========
/**
 * 模糊半径
 * 控制高斯模糊的范围，值越大模糊效果越强
 * 仅在高斯模糊模式下使用
 */
uniform float blur_radius : hint_range(0.0, 10.0) = 2.0;

/**
 * 模糊质量
 * 控制模糊采样的质量和性能平衡
 * 0 = 低质量（9次采样）
 * 1 = 中等质量（13次采样）
 * 2 = 高质量（25次采样）
 * 仅在高斯模糊模式下使用
 */
uniform int blur_quality : hint_range(0, 2) = 1;

// ========== 纹理输入 ==========
/**
 * 屏幕纹理
 * 用于获取背景内容实现毛玻璃效果
 */
uniform sampler2D screen_texture : hint_screen_texture, repeat_disable, filter_linear;

/**
 * 噪声纹理
 * 用于产生扭曲效果的噪声纹理
 * 如果不提供，将使用程序化噪声
 */
uniform sampler2D noise_texture : repeat_enable, filter_linear;

/**
 * 是否使用噪声纹理
 * 如果为 false，将使用程序化噪声
 */
uniform bool use_noise_texture = false;

// ========== 辅助函数 ==========
/**
 * 简单程序化噪声函数
 * 当没有提供噪声纹理时使用
 * 生成基于位置的静态噪声
 */
vec2 simple_noise(vec2 pos) {
    return fract(vec2(
        sin(dot(pos, vec2(12.9898, 78.233))) * 43758.5453,
        sin(dot(pos, vec2(93.9898, 67.345))) * 28653.2341
    ));
}

/**
 * 高效高斯权重计算函数
 * 基于优化的高斯分布计算
 */
float gaussian_weight(vec2 offset, float sigma) {
    return exp(-0.5 * dot(offset / sigma, offset / sigma)) / (6.28318530718 * sigma * sigma);
}

/**
 * 高效高斯模糊函数
 * 参考 Shadertoy 优化实现，使用 MIPmap 加速
 * 16x acceleration by applying gaussian at intermediate MIPmap level
 */
vec4 efficient_gaussian_blur(vec2 uv, float radius, int quality) {
    vec2 texture_size = vec2(textureSize(screen_texture, 0));
    vec2 scale = 1.0 / texture_size;

    // 根据质量调整采样参数
    int samples;
    int lod;
    float sigma;

    if (quality == 0) {
        // 低质量：快速模糊
        samples = 9;
        lod = 0;
        sigma = float(samples) * 0.25;
    } else if (quality == 1) {
        // 中等质量：平衡模式
        samples = 25;
        lod = 1;
        sigma = float(samples) * 0.25;
    } else {
        // 高质量：最佳效果
        samples = 35;
        lod = 2;
        sigma = float(samples) * 0.25;
    }

    int s_lod = 1 << lod; // tile size = 2^lod
    int s = samples / s_lod;
    if (s < 1) s = 1; // 确保至少有1个采样

    vec4 color = vec4(0.0);
    float total_weight = 0.0;

    // 优化的采样循环
    for (int i = 0; i < s * s; i++) {
        vec2 d = vec2(float(i % s), float(i / s)) * float(s_lod) - float(samples) * 0.5;
        vec2 offset = scale * d * radius;

        float weight = gaussian_weight(d, sigma);

        // 使用 textureLod 进行 MIPmap 采样以提高性能
        vec4 sample_color = textureLod(screen_texture, uv + offset, float(lod));

        color += sample_color * weight;
        total_weight += weight;
    }

    // 归一化
    if (total_weight > 0.0) {
        color /= total_weight;
    } else {
        // 如果权重为0，返回原始颜色
        color = texture(screen_texture, uv);
    }

    return color;
}

/**
 * 静态扭曲计算函数
 * 计算基于噪声的静态UV扭曲，覆盖整个区域
 */
vec2 calculate_distortion(vec2 uv) {
    // 使用缩放后的UV坐标采样噪声
    vec2 noise_uv = uv * noise_scale;

    // 获取噪声值
    vec2 noise_value;
    if (use_noise_texture) {
        noise_value = texture(noise_texture, noise_uv).xy;
        // 将噪声值从 [0,1] 范围转换到 [-1,1] 范围
        noise_value = (noise_value - 0.5) * 2.0;
    } else {
        noise_value = simple_noise(noise_uv * 10.0);
        // 将噪声值从 [0,1] 范围转换到 [-1,1] 范围
        noise_value = (noise_value - 0.5) * 2.0;
    }

    // 应用扭曲，整个区域都有扭曲效果
    vec2 distortion = noise_value * noise_distortion * distortion_strength;

    return uv + distortion;
}

void fragment() {
    vec4 final_color;

    if (effect_mode == 0) {
        // 毛玻璃扭曲效果
        vec2 distorted_uv = calculate_distortion(UV);

        // 将UV坐标转换为屏幕坐标进行采样
        vec2 screen_uv = SCREEN_UV + (distorted_uv - UV);

        // 确保屏幕坐标在有效范围内
        screen_uv = clamp(screen_uv, 0.0, 1.0);

        // 采样背景纹理
        final_color = texture(screen_texture, screen_uv);

    } else if (effect_mode == 1) {
        // 高斯模糊效果
        final_color = efficient_gaussian_blur(SCREEN_UV, blur_radius, blur_quality);
    } else {
        // 默认情况：直接采样背景
        final_color = texture(screen_texture, SCREEN_UV);
    }

    // 应用透明度
    COLOR = vec4(final_color.rgb, alpha);
}
