shader_type canvas_item;

// 定义要混合的纹理
uniform sampler2D texture_1 : source_color, hint_default_black, repeat_enable;
uniform sampler2D texture_2 : source_color, hint_default_white, repeat_enable;
uniform sampler2D normal_map_1 : hint_normal, repeat_enable;
uniform sampler2D normal_map_2 : hint_normal, repeat_enable;

// 用于混合的噪声纹理
uniform sampler2D noise_texture : repeat_enable;
// 是否启用法线贴图
uniform bool use_normal_mapping = false;

// 过渡深度，用于控制混合边缘的宽度（0 - 1）
uniform float transition_depth : hint_range(0.0, 1.0) = 0.5;

// 纹理采样缩放，分别调整两张纹理在屏幕上的密度
uniform vec2 uv_scale_1 = vec2(1.5, 1.5);
uniform vec2 uv_scale_2 = vec2(1.5, 1.5);

// 阈值参数：black_threshold 以下完全使用 texture_2，white_threshold 以上完全使用 texture_1
uniform float black_threshold : hint_range(0.0, 1.0) = 0.2;
uniform float white_threshold : hint_range(0.0, 1.0) = 0.8;

// 全局滚动速度
uniform vec2 scroll_speed = vec2(0.0, 0.0);


// ---- 抗重复平铺参数 ----
// 抗重复平铺模式: 0 = 关闭, 1 = 偏移 (IQ), 2 = 旋转
uniform int anti_tile_mode : hint_range(0, 2) = 0;

// -- 偏移模式参数 --
// 用于驱动随机偏移的低频噪声纹理
uniform sampler2D no_tile_noise : repeat_enable, hint_default_black;
// 抗重复噪声的采样频率
uniform float no_tile_noise_freq = 0.005;
// 贴图1的随机偏移强度
uniform float no_tile_offset_strength_1 : hint_range(0.0, 1.0) = 0.1;
// 贴图2的随机偏移强度
uniform float no_tile_offset_strength_2 : hint_range(0.0, 1.0) = 0.1;


// -- Helper Functions --

// ## 通用 ##
// 计算 vec3 各分量之和
float sum(vec3 v) {
	return v.x + v.y + v.z;
}

// ## 偏移模式 (IQ) ##
// 通过混合多个随机偏移的采样来渲染纹理，以消除平铺感。
vec4 texture_offset(sampler2D tex, vec2 uv, float offset_strength) {
	vec2 duvdx = dFdx(uv);
    vec2 duvdy = dFdy(uv);

    float k = texture(no_tile_noise, no_tile_noise_freq * uv).r;

    float l = k * 8.0;
    float f = fract(l);

    float ia = floor(l);
    float ib = ia + 1.0;

    vec2 offa = sin(vec2(3.0, 7.0) * ia);
    vec2 offb = sin(vec2(3.0, 7.0) * ib);

    vec4 cola = textureGrad(tex, uv + offset_strength * offa, duvdx, duvdy);
    vec4 colb = textureGrad(tex, uv + offset_strength * offb, duvdx, duvdy);

    return mix(cola, colb, smoothstep(0.2, 0.8, f - 0.1 * sum(cola.rgb - colb.rgb)));
}

// ## 旋转模式 ##
// 2D 旋转矩阵
mat2 rot(float a) {
    float c = cos(a);
    float s = sin(a);
    // GLSL的 mat2(c, s, -s, c) 对应 Godot shader 的列向量构造
    return mat2(vec2(c, s), vec2(-s, c));
}

// 伪随机数生成器 (哈希函数)
float rand(vec2 p, float seed) {
    return fract(sin(dot(p, vec2(12.9898, 78.233)) + seed) * 43758.5453123);
}

// 通过混合多个随机旋转的采样来渲染纹理
vec4 texture_rotate(sampler2D tex, vec2 uv) {
    vec2 id = floor(uv);
    vec2 sv = smoothstep(0.0, 1.0, fract(uv));
    float seed = 9.78376; // 随机种子

    // 为当前及相邻瓦片生成随机旋转角度
    float self_rot = rand(id, seed) * 6.28318; // 2 * PI
    float right_rot = rand(id + vec2(1.0, 0.0), seed) * 6.28318;
    float top_rot = rand(id + vec2(0.0, 1.0), seed) * 6.28318;
    float top_right_rot = rand(id + vec2(1.0, 1.0), seed) * 6.28318;

    vec2 dx = dFdx(uv);
    vec2 dy = dFdy(uv);

    // 对四个角的瓦片进行独立旋转采样
    vec4 s1 = textureGrad(tex, uv * rot(self_rot), dx, dy);
    vec4 s2 = textureGrad(tex, uv * rot(right_rot), dx, dy);
    vec4 s3 = textureGrad(tex, uv * rot(top_rot), dx, dy);
    vec4 s4 = textureGrad(tex, uv * rot(top_right_rot), dx, dy);

    // 双线性插值，平滑混合四个采样结果
    return mix(mix(s1, s2, sv.x), mix(s3, s4, sv.x), sv.y);
}

void light() {
    vec3 albedo = COLOR.rgb;
    vec3 N = normalize(NORMAL);
    vec3 L = normalize(LIGHT_DIRECTION);
    float lambert = use_normal_mapping ? max(dot(N, L), 0.0) : 1.0;
    vec3 light_col = LIGHT_COLOR.rgb * LIGHT_ENERGY;
    LIGHT = vec4(albedo * light_col * lambert, COLOR.a);
}

void fragment() {
	vec4 tex1;
	vec4 tex2;
	// 为法线贴图设置一个默认的“平面”值。
	// 如果 use_normal_mapping 为 false，将使用此值。
	vec4 norm1 = vec4(0.5, 0.5, 1.0, 1.0);
	vec4 norm2 = vec4(0.5, 0.5, 1.0, 1.0);

	// 首先应用全局滚动
	vec2 scrolled_uv = UV + scroll_speed * TIME;

	vec2 scaled_uv1 = scrolled_uv * uv_scale_1;
	vec2 scaled_uv2 = scrolled_uv * uv_scale_2;

	if (anti_tile_mode == 1) {
		// --- 模式 1: 偏移 (IQ) ---
		tex1 = texture_offset(texture_1, scaled_uv1, no_tile_offset_strength_1);
		tex2 = texture_offset(texture_2, scaled_uv2, no_tile_offset_strength_2);
		if (use_normal_mapping) {
			norm1 = texture_offset(normal_map_1, scaled_uv1, no_tile_offset_strength_1);
			norm2 = texture_offset(normal_map_2, scaled_uv2, no_tile_offset_strength_2);
		}
	} else if (anti_tile_mode == 2) {
		// --- 模式 2: 旋转 ---
		tex1 = texture_rotate(texture_1, scaled_uv1);
		tex2 = texture_rotate(texture_2, scaled_uv2);
		if (use_normal_mapping) {
			norm1 = texture_rotate(normal_map_1, scaled_uv1);
			norm2 = texture_rotate(normal_map_2, scaled_uv2);
		}
	} else {
		// --- 模式 0: 关闭 (标准平铺) ---
		vec2 tiled_uv1 = fract(scaled_uv1);
		vec2 tiled_uv2 = fract(scaled_uv2);
		tex1 = texture(texture_1, tiled_uv1);
		tex2 = texture(texture_2, tiled_uv2);
		if (use_normal_mapping) {
			norm1 = texture(normal_map_1, tiled_uv1);
			norm2 = texture(normal_map_2, tiled_uv2);
		}
	}

	// --- 混合逻辑保持不变 ---
	// 获取用于混合的噪声值
	float blend_factor = smoothstep(black_threshold, white_threshold, texture(noise_texture, scrolled_uv).r);

	// 根据混合因子混合颜色和法线
	COLOR = mix(tex2, tex1, blend_factor);
	NORMAL_MAP = mix(norm2, norm1, blend_factor).rgb;
}
