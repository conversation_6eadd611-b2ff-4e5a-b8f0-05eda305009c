[gd_resource type="VisualShader" load_steps=40 format=3 uid="uid://c4ijmxnenptwl"]

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_mwkwj"]
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_kbgch"]
input_name = "uv"

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_mwkwj"]
default_input_values = [0, 0.0, 1, 0.2]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_kbgch"]
default_input_values = [0, 0.0, 1, 0.1]
operator = 2

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_do2ug"]
default_input_values = [0, 0.0, 1, 0.05]
operator = 2

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_sjtgc"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_ess6d"]

[sub_resource type="VisualShaderNodeTexture2DParameter" id="VisualShaderNodeTexture2DParameter_ctriw"]
parameter_name = "offset"
texture_repeat = 1

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_mwkwj"]
parameter_name = "BaseColor"
default_value_enabled = true
default_value = Color(1, 0.12549, 0.0862745, 1)

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_kbgch"]
parameter_name = "BaseColor2"
default_value_enabled = true
default_value = Color(0.4, 0, 0, 1)

[sub_resource type="VisualShaderNodeVec2Parameter" id="VisualShaderNodeVec2Parameter_mwkwj"]
parameter_name = "OffsetDir"
default_value_enabled = true
default_value = Vector2(1, 0)

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_kbgch"]
default_input_values = [0, Vector2(0, 0), 1, Vector2(0, 0)]
op_type = 0
operator = 2

[sub_resource type="VisualShaderNodeFloatParameter" id="VisualShaderNodeFloatParameter_mwkwj"]
parameter_name = "OffsetSpeed"
default_value_enabled = true
default_value = 0.05

[sub_resource type="VisualShaderNodeVectorDecompose" id="VisualShaderNodeVectorDecompose_mwkwj"]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_ess6d"]
input_name = "time"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_c1n0a"]

[sub_resource type="VisualShaderNodeFloatParameter" id="VisualShaderNodeFloatParameter_gh1j1"]
parameter_name = "EdgeWaveSpeed"
default_value_enabled = true
default_value = 0.5

[sub_resource type="VisualShaderNodeFloatParameter" id="VisualShaderNodeFloatParameter_kbgch"]
parameter_name = "EdgeWaveTense"
default_value_enabled = true
default_value = 5.0

[sub_resource type="FastNoiseLite" id="FastNoiseLite_mwkwj"]
noise_type = 3
frequency = 0.0033
fractal_type = 2
fractal_octaves = 3
fractal_lacunarity = 0.295

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_kbgch"]
width = 4012
seamless_blend_skirt = 0.0
noise = SubResource("FastNoiseLite_mwkwj")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_do2ug"]
source = 5
texture = SubResource("NoiseTexture2D_kbgch")

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_sjtgc"]
operator = 2

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_ess6d"]
function = 0

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_mwkwj"]

[sub_resource type="VisualShaderNodeFloatParameter" id="VisualShaderNodeFloatParameter_do2ug"]
parameter_name = "EdgeWaveScale"
default_value_enabled = true
default_value = 0.5

[sub_resource type="VisualShaderNodeFloatParameter" id="VisualShaderNodeFloatParameter_sjtgc"]
parameter_name = "EdgeWaveLevel"
default_value_enabled = true
default_value = 50.0

[sub_resource type="VisualShaderNodeIf" id="VisualShaderNodeIf_ess6d"]
default_input_values = [0, 0.0, 1, 0.0, 2, 1e-05, 3, Vector3(0, 0, 0), 4, Vector3(0, 0, 0), 5, Vector3(1, 1, 1)]
expanded_output_ports = [0]

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_mwkwj"]
function = 31

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_kbgch"]
function = 31

[sub_resource type="VisualShaderNodeFloatFunc" id="VisualShaderNodeFloatFunc_do2ug"]
function = 31

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_ess6d"]
default_input_values = [0, 0.0, 1, 100.0]
operator = 3

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_ctriw"]
default_input_values = [0, 0.0, 1, 100.0]
operator = 3

[sub_resource type="VisualShaderNodeFloatOp" id="VisualShaderNodeFloatOp_c1n0a"]
operator = 1

[sub_resource type="VisualShaderNodeIf" id="VisualShaderNodeIf_gh1j1"]
default_input_values = [0, 0.0, 1, 100.0, 2, 1e-05, 3, Vector3(0, 0, 0), 4, Vector3(0, 0, 0), 5, Vector3(0, 0, 0)]

[sub_resource type="VisualShaderNodeMix" id="VisualShaderNodeMix_do2ug"]
output_port_for_preview = 0
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(1, 1, 1), 2, Vector3(0.5, 0.5, 0.5)]
op_type = 3

[sub_resource type="VisualShaderNodeTexture2DParameter" id="VisualShaderNodeTexture2DParameter_mwkwj"]
parameter_name = "WaveNoise"
texture_repeat = 1

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_kbgch"]
source = 5

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_do2ug"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_sjtgc"]

[resource]
resource_local_to_scene = true
code = "shader_type canvas_item;
render_mode blend_mix;

uniform vec4 BaseColor : source_color = vec4(1.000000, 0.125490, 0.086274, 1.000000);
uniform vec4 BaseColor2 : source_color = vec4(0.400000, 0.000000, 0.000000, 1.000000);
uniform sampler2D WaveNoise : repeat_enable;
uniform float OffsetSpeed = 0.05000000074506;
uniform vec2 OffsetDir = vec2(1.000000, 0.000000);
uniform sampler2D offset : repeat_enable;
uniform float EdgeWaveTense = 5.0;
uniform float EdgeWaveSpeed = 0.5;
uniform float EdgeWaveScale = 0.5;
uniform float EdgeWaveLevel = 50.0;



void fragment() {
// ColorParameter:19
	vec4 n_out19p0 = BaseColor;


// ColorParameter:20
	vec4 n_out20p0 = BaseColor2;


// Input:8
	float n_out8p0 = TIME;


// FloatOp:12
	float n_in12p1 = 0.20000;
	float n_out12p0 = n_out8p0 * n_in12p1;


// UVFunc:9
	vec2 n_in9p1 = vec2(1.00000, 1.00000);
	vec2 n_out9p0 = vec2(n_out12p0) * n_in9p1 + UV;


	vec4 n_out7p0;
// Texture2D:7
	n_out7p0 = texture(WaveNoise, n_out9p0);


// FloatOp:13
	float n_in13p1 = 0.10000;
	float n_out13p0 = n_out7p0.x * n_in13p1;


// Input:11
	vec2 n_out11p0 = UV;


// VectorOp:10
	vec2 n_out10p0 = vec2(n_out13p0) + n_out11p0;


// Input:15
	float n_out15p0 = TIME;


// FloatParameter:23
	float n_out23p0 = OffsetSpeed;


// FloatOp:14
	float n_out14p0 = n_out15p0 * n_out23p0;


// Vector2Parameter:21
	vec2 n_out21p0 = OffsetDir;


// VectorOp:22
	vec2 n_out22p0 = vec2(n_out14p0) * n_out21p0;


// UVFunc:16
	vec2 n_in16p1 = vec2(1.00000, 1.00000);
	vec2 n_out16p0 = n_out22p0 * n_in16p1 + n_out10p0;


	vec4 n_out3p0;
// Texture2D:3
	n_out3p0 = texture(offset, n_out16p0);


// Mix:5
	vec3 n_out5p0 = mix(vec3(n_out19p0.xyz), vec3(n_out20p0.xyz), vec3(n_out3p0.xyz));


// FloatParameter:29
	float n_out29p0 = EdgeWaveTense;


// FloatParameter:28
	float n_out28p0 = EdgeWaveSpeed;


// Input:25
	float n_out25p0 = TIME;


// VectorDecompose:24
	float n_out24p0 = vec3(n_out11p0, 0.0).x;
	float n_out24p1 = vec3(n_out11p0, 0.0).y;
	float n_out24p2 = vec3(n_out11p0, 0.0).z;


// FloatFunc:37
	float n_out37p0 = 1.0 - n_out24p1;


// MultiplyAdd:27
	float n_out27p0 = (n_out28p0 * n_out25p0) + n_out37p0;


// FloatOp:30
	float n_out30p0 = n_out29p0 * n_out27p0;


// FloatFunc:31
	float n_out31p0 = sin(n_out30p0);


// FloatParameter:33
	float n_out33p0 = EdgeWaveScale;


// FloatOp:40
	float n_in40p1 = 100.00000;
	float n_out40p0 = n_out33p0 / n_in40p1;


// FloatParameter:34
	float n_out34p0 = EdgeWaveLevel;


// FloatOp:41
	float n_out41p0 = n_out34p0 - n_out33p0;


	vec3 n_out42p0;
// If:42
	float n_in42p1 = 100.00000;
	float n_in42p2 = 0.00001;
	if(abs(n_out34p0 - n_in42p1) < n_in42p2)
	{
		n_out42p0 = vec3(n_out34p0);
	}
	else if(n_out34p0 < n_in42p1)
	{
		n_out42p0 = vec3(n_out41p0);
	}
	else
	{
		n_out42p0 = vec3(n_out41p0);
	}


// FloatOp:39
	float n_in39p1 = 100.00000;
	float n_out39p0 = n_out42p0.x / n_in39p1;


// FloatFunc:38
	float n_out38p0 = 1.0 - n_out39p0;


// MultiplyAdd:32
	float n_out32p0 = (n_out31p0 * n_out40p0) + n_out38p0;


// FloatFunc:36
	float n_out36p0 = 1.0 - n_out24p0;


	vec3 n_out35p0;
// If:35
	float n_in35p2 = 0.00001;
	vec3 n_in35p3 = vec3(0.00000, 0.00000, 0.00000);
	vec3 n_in35p4 = vec3(0.00000, 0.00000, 0.00000);
	vec3 n_in35p5 = vec3(1.00000, 1.00000, 1.00000);
	if(abs(n_out32p0 - n_out36p0) < n_in35p2)
	{
		n_out35p0 = n_in35p3;
	}
	else if(n_out32p0 < n_out36p0)
	{
		n_out35p0 = n_in35p5;
	}
	else
	{
		n_out35p0 = n_in35p4;
	}
	float n_out35p1 = n_out35p0.r;


// Output:0
	COLOR.rgb = n_out5p0;
	COLOR.a = n_out35p1;


}
"
mode = 1
flags/light_only = false
nodes/fragment/0/position = Vector2(940, 1640)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_do2ug")
nodes/fragment/3/position = Vector2(-800, 700)
nodes/fragment/5/node = SubResource("VisualShaderNodeMix_do2ug")
nodes/fragment/5/position = Vector2(-100, -200)
nodes/fragment/6/node = SubResource("VisualShaderNodeTexture2DParameter_mwkwj")
nodes/fragment/6/position = Vector2(-3440, -100)
nodes/fragment/7/node = SubResource("VisualShaderNodeTexture_kbgch")
nodes/fragment/7/position = Vector2(-1600, -580)
nodes/fragment/8/node = SubResource("VisualShaderNodeInput_do2ug")
nodes/fragment/8/position = Vector2(-3420, -540)
nodes/fragment/9/node = SubResource("VisualShaderNodeUVFunc_sjtgc")
nodes/fragment/9/position = Vector2(-2140, -680)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_mwkwj")
nodes/fragment/10/position = Vector2(-1540, -60)
nodes/fragment/11/node = SubResource("VisualShaderNodeInput_kbgch")
nodes/fragment/11/position = Vector2(-3580, 1600)
nodes/fragment/12/node = SubResource("VisualShaderNodeFloatOp_mwkwj")
nodes/fragment/12/position = Vector2(-2680, -580)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatOp_kbgch")
nodes/fragment/13/position = Vector2(-1180, -660)
nodes/fragment/14/node = SubResource("VisualShaderNodeFloatOp_do2ug")
nodes/fragment/14/position = Vector2(-2440, 660)
nodes/fragment/15/node = SubResource("VisualShaderNodeInput_sjtgc")
nodes/fragment/15/position = Vector2(-3240, 680)
nodes/fragment/16/node = SubResource("VisualShaderNodeUVFunc_ess6d")
nodes/fragment/16/position = Vector2(-1540, 540)
nodes/fragment/18/node = SubResource("VisualShaderNodeTexture2DParameter_ctriw")
nodes/fragment/18/position = Vector2(-1520, 980)
nodes/fragment/19/node = SubResource("VisualShaderNodeColorParameter_mwkwj")
nodes/fragment/19/position = Vector2(-880, -320)
nodes/fragment/20/node = SubResource("VisualShaderNodeColorParameter_kbgch")
nodes/fragment/20/position = Vector2(-880, 160)
nodes/fragment/21/node = SubResource("VisualShaderNodeVec2Parameter_mwkwj")
nodes/fragment/21/position = Vector2(-2540, 1000)
nodes/fragment/22/node = SubResource("VisualShaderNodeVectorOp_kbgch")
nodes/fragment/22/position = Vector2(-1940, 900)
nodes/fragment/23/node = SubResource("VisualShaderNodeFloatParameter_mwkwj")
nodes/fragment/23/position = Vector2(-3200, 960)
nodes/fragment/24/node = SubResource("VisualShaderNodeVectorDecompose_mwkwj")
nodes/fragment/24/position = Vector2(-2460, 1860)
nodes/fragment/25/node = SubResource("VisualShaderNodeInput_ess6d")
nodes/fragment/25/position = Vector2(-2320, 2740)
nodes/fragment/27/node = SubResource("VisualShaderNodeMultiplyAdd_c1n0a")
nodes/fragment/27/position = Vector2(-1540, 2440)
nodes/fragment/28/node = SubResource("VisualShaderNodeFloatParameter_gh1j1")
nodes/fragment/28/position = Vector2(-2280, 2260)
nodes/fragment/29/node = SubResource("VisualShaderNodeFloatParameter_kbgch")
nodes/fragment/29/position = Vector2(-1620, 1860)
nodes/fragment/30/node = SubResource("VisualShaderNodeFloatOp_sjtgc")
nodes/fragment/30/position = Vector2(-1041.64, 2159.68)
nodes/fragment/31/node = SubResource("VisualShaderNodeFloatFunc_ess6d")
nodes/fragment/31/position = Vector2(-1060, 2560)
nodes/fragment/32/node = SubResource("VisualShaderNodeMultiplyAdd_mwkwj")
nodes/fragment/32/position = Vector2(-1060, 2820)
nodes/fragment/33/node = SubResource("VisualShaderNodeFloatParameter_do2ug")
nodes/fragment/33/position = Vector2(-2440, 2940)
nodes/fragment/34/node = SubResource("VisualShaderNodeFloatParameter_sjtgc")
nodes/fragment/34/position = Vector2(-2760, 3440)
nodes/fragment/35/node = SubResource("VisualShaderNodeIf_ess6d")
nodes/fragment/35/position = Vector2(-480, 2820)
nodes/fragment/36/node = SubResource("VisualShaderNodeFloatFunc_mwkwj")
nodes/fragment/36/position = Vector2(-2020, 1780)
nodes/fragment/37/node = SubResource("VisualShaderNodeFloatFunc_kbgch")
nodes/fragment/37/position = Vector2(-2020, 2000)
nodes/fragment/38/node = SubResource("VisualShaderNodeFloatFunc_do2ug")
nodes/fragment/38/position = Vector2(-1040, 3440)
nodes/fragment/39/node = SubResource("VisualShaderNodeFloatOp_ess6d")
nodes/fragment/39/position = Vector2(-1500, 3420)
nodes/fragment/40/node = SubResource("VisualShaderNodeFloatOp_ctriw")
nodes/fragment/40/position = Vector2(-1480, 2880)
nodes/fragment/41/node = SubResource("VisualShaderNodeFloatOp_c1n0a")
nodes/fragment/41/position = Vector2(-2000, 3420)
nodes/fragment/42/node = SubResource("VisualShaderNodeIf_gh1j1")
nodes/fragment/42/position = Vector2(-1980, 3820)
nodes/fragment/connections = PackedInt32Array(3, 0, 5, 2, 6, 0, 7, 2, 9, 0, 7, 0, 12, 0, 9, 2, 7, 0, 13, 0, 13, 0, 10, 0, 10, 0, 16, 0, 18, 0, 3, 2, 16, 0, 3, 0, 19, 0, 5, 0, 20, 0, 5, 1, 21, 0, 22, 1, 14, 0, 22, 0, 22, 0, 16, 2, 23, 0, 14, 1, 28, 0, 27, 0, 29, 0, 30, 0, 27, 0, 30, 1, 30, 0, 31, 0, 31, 0, 32, 0, 32, 0, 35, 0, 36, 0, 35, 1, 37, 0, 27, 2, 24, 1, 37, 0, 24, 0, 36, 0, 38, 0, 32, 2, 39, 0, 38, 0, 33, 0, 40, 0, 40, 0, 32, 1, 34, 0, 41, 0, 33, 0, 41, 1, 34, 0, 42, 0, 34, 0, 42, 3, 41, 0, 42, 4, 41, 0, 42, 5, 42, 0, 39, 0, 35, 1, 0, 1, 5, 0, 0, 0, 8, 0, 12, 0, 15, 0, 14, 0, 11, 0, 24, 0, 25, 0, 27, 1, 11, 0, 10, 1)
