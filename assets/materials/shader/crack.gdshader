shader_type canvas_item;
// Crack shader ported from the ShaderToy "Vorocracks" variant.
// Original author(s): see https://www.shadertoy.com/view/lsVyRy and https://www.shadertoy.com/view/Xd3fRN
// Ported to Godot 4.x shading language by <your name>.

// ------------------------------------------------------------
// USER-TWEAKABLE UNIFORMS
// ------------------------------------------------------------
// Pattern scale controls.
uniform vec2 u_scale = vec2(4.0, 4.0);
// Pattern position offset.
instance uniform vec2 u_offset = vec2(0.0, 0.0);
// Horizontal scrolling speed.
uniform float u_speed : hint_range(0.0, 5.0) = 1.0;
// Background color (no checkerboard).
uniform vec4 background_color : source_color = vec4(0.0, 0.0, 0.0, 0.0);
// Crack color tint.
uniform vec4 crack_color : source_color = vec4(0.9, 0.85, 0.85, 1.0);
// Crack width (thickness) control.
uniform float crack_width : hint_range(0.0, 1.0) = 0.0;
// Edge fade effect (0.0 = no fade, 1.0 = max fade from edges).
uniform float edge_fade : hint_range(0.0, 1.0) = 0.0;
// Edge fade smoothness (higher = smoother transition).
uniform float edge_smoothness : hint_range(0.01, 1.0) = 0.2;

// ------------------------------------------------------------
// CONSTANTS (feel free to turn into uniforms if you want)
// ------------------------------------------------------------
const bool   VARIANT               = true;  // true: amplifies Voronoi cell jittering
const float  ofs                   = (VARIANT ? 0.5 : 0.0);

const float  RATIO                 = 1.0;   // stone length / width ratio
const int    CRACK_DEPTH           = 3;     // number of FBM / Voronoi octaves
const float  CRACK_ZEBRA_SCALE     = 1.0;
const float  CRACK_ZEBRA_AMP       = 0.67;
const float  CRACK_PROFILE         = 1.0;
const float  CRACK_SLOPE           = 50.0;

// ------------------------------------------------------------
// HELPER FUNCTIONS
// ------------------------------------------------------------

mat2 rot(float a) {
    float c = cos(a);
    float s = sin(a);
    // Godot expects column vectors when building a matrix.
    return mat2(vec2(c, -s), vec2(s, c));
}

// === Voronoi =====================================================
const mat2 HASH22_MAT = mat2(vec2(127.1, 269.5), vec2(311.7, 183.3));
// Using matrix * vector (column-major) multiplication.
#define hash22(p)  fract(18.5453 * sin(HASH22_MAT * (p)))

vec2 disp(vec2 p) {
    return -ofs + (1.0 + 2.0 * ofs) * hash22(p);
}

// Base Voronoi: returns vec3(distance, id.x, id.y)
vec3 voronoiB(vec2 u) {
    vec2 iu = floor(u);
    vec2 C = vec2(0.0);
    vec2 P = vec2(0.0);
    float m = 1e9;
    float d;

    // first pass: find closest cell center
    for (int k = 0; k < 25; k++) {
        vec2 p = iu + vec2(float(k % 5 - 2), float(k / 5 - 2));
        vec2 o = disp(p);
        vec2 r = p - u + o;
        d = dot(r, r);
        if (d < m) {
            m = d;
            C = p - iu;
            P = r;
        }
    }

    // second pass: distance to nearest border
    m = 1e9;
    for (int k = 0; k < 25; k++) {
        vec2 p = iu + C + vec2(float(k % 5 - 2), float(k / 5 - 2));
        vec2 o = disp(p);
        vec2 r = p - u + o;
        if (dot(P - r, P - r) > 1e-5) {
            m = min(m, 0.5 * dot((P + r), normalize(r - P)));
        }
    }

    return vec3(m, P + u);
}

// === Pseudo-Perlin FBM ==========================================
#define hash21(p) fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453123)

const int MOD = 1; // 0: 0..1  | 1: -1..1 | 2: abs(-1..1) | 3: inverted abs

float noise2(vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);
    f = f * f * (3.0 - 2.0 * f); // smoothstep

    float v = mix(
        mix(hash21(i + vec2(0.0, 0.0)), hash21(i + vec2(1.0, 0.0)), f.x),
        mix(hash21(i + vec2(0.0, 1.0)), hash21(i + vec2(1.0, 1.0)), f.x), f.y);

    return (MOD == 0) ? v : (MOD == 1) ? 2.0 * v - 1.0 : (MOD == 2) ? abs(2.0 * v - 1.0) : 1.0 - abs(2.0 * v - 1.0);
}

float fbm2(vec2 p) {
    float v = 0.0;
    float a = 0.5;
    mat2 R = rot(0.37);
    for (int i = 0; i < 9; i++) {
        p *= R;
        v += a * noise2(p);
        p *= 2.0;
        a *= 0.5;
    }
    return v;
}

vec2 noise22(vec2 p) {
    return vec2(noise2(p), noise2(p + 17.7));
}

vec2 fbm22(vec2 p) {
    vec2 v = vec2(0.0);
    float a = 0.5;
    mat2 R = rot(0.37);
    for (int i = 0; i < 6; i++) {
        p *= R;
        v += a * noise22(p);
        p *= 2.0;
        a *= 0.5;
    }
    return v;
}

// ------------------------------------------------------------
// FRAGMENT (main)
// ------------------------------------------------------------
void fragment() {
    // Use screen‐space normalized UV so pattern is independent from sprite texture UV.
    // vec2 U = (SCREEN_UV + u_offset) * u_scale;
    vec2 U = (UV + u_offset) * u_scale;
    // horizontal scrolling like in ShaderToy demo
    U.x += TIME * u_speed;

    vec4 col = vec4(0.0);
    vec3 H0 = vec3(0.0);
    vec2 Uw = U; // working copy we mutate per octave

    for (int octave = 0; octave < CRACK_DEPTH; octave++) {
        vec2 V = Uw / vec2(RATIO, 1.0);
        vec2 D = CRACK_ZEBRA_AMP * fbm22(Uw / CRACK_ZEBRA_SCALE) * CRACK_ZEBRA_SCALE;
        vec3 H = voronoiB(V + D);
        if (octave == 0) {
            H0 = H;
        }

        float d = H.x; // distance to cracks
        d = min(1.0, CRACK_SLOPE * pow(max(0.0, d - crack_width), CRACK_PROFILE));
        col += vec4(1.0 - d) / exp2(float(octave));

        Uw *= 1.5 * rot(0.37);
    }

    // Use intensity (grayscale) from col.r to mix crack color and background.
    float intensity = clamp(col.r, 0.0, 1.0);
    // Mix colors including alpha channel for background transparency
    vec4 final_color = mix(background_color, crack_color, intensity);

    // Apply edge fading effect if enabled
    if (edge_fade > 0.0) {
        // Calculate distance from UV center (0.5, 0.5)
        vec2 center_dist = abs(UV - 0.5) * 2.0; // 0 at center, 1 at edges
        float edge_factor = max(center_dist.x, center_dist.y);

        // Create smooth fade using smoothstep
        float fade_amount = 1.0 - smoothstep(1.0 - edge_fade - edge_smoothness,
                                            1.0 - edge_smoothness,
                                            edge_factor);

        // Apply fade to alpha channel
        final_color.a *= fade_amount;
    }

    COLOR = final_color;
}
