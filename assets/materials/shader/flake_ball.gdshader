shader_type canvas_item;
render_mode unshaded;

// Converted from <PERSON><PERSON><PERSON> (<PERSON><PERSON> / <PERSON> 2015)
// Original: https://www.shadertoy.com/view/4d3GDn (or similar)
// Ported to Godot CanvasItem shader language.

// --- Uniforms replicating <PERSON><PERSON><PERSON> inputs --------------------------------
uniform sampler2D iChannel0;  // Environment / cube map projected to equirectangular or any texture
uniform vec2  iResolution = vec2(720.0, 720.0); // Render target size (pixels) – override from script if desired
// Optional: make background transparent (areas outside the main sphere)
uniform bool background_transparent  = true;

// --- Customizable parameters --------------------------------------------
uniform float time_scale        = 1.0;               // Multiplier for animation speed
uniform vec3  sphere_color       : source_color = vec3(1.0, 1.0, 1.0); // Tint for main sphere surface
uniform vec3  displacement_color : source_color = vec3(0.8, 0.9, 1.0); // Tint for displacement/height colors
uniform float sphere_radius     = 5.0;               // Base radius of the sphere
uniform float displacement_scale = 3.0;              // Height map amplitude

// --- Lighting & look controls -------------------------------------------
uniform float light_intensity      = 0.6;  // Overall diffuse light strength
uniform float reflection_intensity = 0.45; // Cube reflection multiplier
uniform float refraction_intensity = 0.8;  // Cube refraction multiplier
uniform float refraction_angle     = 0.7;  // Ratio for refract()
uniform float base_brightness      = 0.35; // Controls pow(b, X) term start
uniform float specular_power       = 16.0; // Specular exponent
uniform float displacement_blend   = 0.5;  // Mix factor for displacement colour
uniform float hash_speed           = 0.2;  // Speed factor for Voronoi hash

//--------------------------------------------------------------------------

#define displaceOffset 0.095
#define USE_SPHERE_OR_BOX
#define m2pi 6.2831

// --- Helper 2-D noise / voronoi effect -----------------------------------
vec3 effect(vec2 v)
{
    vec2 c0 = vec2(30.0, 20.0);
    vec2 c1 = vec2(10.0, 40.0);

    vec2 n = floor(v);
    vec2 f = fract(v);

    vec3 col;
    col.x = 10.0;

    for (float j = -1.0; j <= 1.0; j += 1.0)
    {
        for (float i = -1.0; i <= 1.0; i += 1.0)
        {
            vec2 g = vec2(i, j);

            vec2 ng = n + g;
            float ng0 = dot(ng, c0);
            float ng1 = dot(ng, c1);
            vec2 ng01 = vec2(ng0, ng1);
            vec2 hash = fract(cos(ng01) * TIME * time_scale * hash_speed);

            vec2 o = sin(m2pi * hash) * 0.5 + 0.5;

            vec2 r = g + o - f;

            float d = dot(r, r);

            if (d < col.x)
                col = vec3(d, r);
        }
    }

    return col.xzz;
}

// --- Height/displacement field -------------------------------------------
vec4 displacement(vec3 p)
{
    vec3 col = effect(p.xz);
    float dist = dot(col, vec3(displaceOffset));
    dist = clamp(dist, 0.0, 1.0);
    return vec4(dist, col * 1.5);
}

// --- Signed-distance helpers ---------------------------------------------
float obox(vec3 p, vec3 b)  { return length(max(abs(p) - b, 0.0)); }
float osphere(vec3 p, float r) { return length(p) - r; }

// --- Scene distance field -------------------------------------------------
vec4 map(vec3 p)
{
    float scale = displacement_scale;
    float dist = 0.0;

    float x = 6.0;
    float z = 6.0;

    vec4 disp = displacement(p);
    float y = 1.0 - smoothstep(0.0, 1.0, disp.x) * scale;

#ifdef USE_SPHERE_OR_BOX
    dist = osphere(p, sphere_radius - y);
#else
    if (p.y > 0.0)
        dist = obox(p, vec3(x, 1.0 - y, z));
    else
        dist = obox(p, vec3(x, 1.0, z));
#endif

    return vec4(dist, disp.yzw);
}

// --- Normal computation ---------------------------------------------------
vec3 calcNormal(vec3 pos)
{
    vec3 eps = vec3(0.05, 0.0, 0.0);
    vec3 nor = vec3(
        map(pos + eps.xyy).x - map(pos - eps.xyy).x,
        map(pos + eps.yxy).x - map(pos - eps.yxy).x,
        map(pos + eps.yyx).x - map(pos - eps.yyx).x);
    return normalize(nor);
}

// --- Ray-marcher ----------------------------------------------------------
float march(vec3 ro, vec3 rd, float rmPrec, float maxd)
{
    float s = rmPrec;
    float d = 0.0;
    vec3  p = ro;
    const int ITER = 250;
    for (int i = 0; i < ITER; i++)
    {
        if (s < 0.0025 * log(d * d / s / 500.0) || s > maxd)
            break;
        s = map(p).x;
        d += abs(s) * 0.2;
        p = ro + rd * d;
    }
    return d;
}

// --- Main fragment --------------------------------------------------------
void fragment()
{
    // Derive fragCoord similar to Shadertoy
    vec2 fragCoord = UV * iResolution;

    float time = TIME * time_scale * 0.3;
    float cam_a = time; // angle around Y axis

#ifdef USE_SPHERE_OR_BOX
    float cam_e = 5.52;
    float cam_d = 1.88;
#else
    float cam_e = 1.0;
    float cam_d = 1.8;
#endif

    // Removed iMouse-dependent camera interaction.

    vec2 s = iResolution;
    vec2 uv = (2.0 * fragCoord - s) / s.y;

    vec3 col = vec3(0.0);
    float alpha = 1.0;

    // Camera setup
    vec3 camUp   = vec3(0.0, 1.0, 0.0);
    vec3 camView = vec3(0.0); // looking at origin

    vec3 ro = vec3(-sin(cam_a) * cam_d, cam_e + 1.0, cos(cam_a) * cam_d);
    vec3 rov = normalize(camView - ro);

    vec3 u = normalize(cross(camUp, rov));
    vec3 v = cross(rov, u);
    vec3 rd = normalize(rov + uv.x * u + uv.y * v);

    // March!
    float prec = 0.025;
    float maxd = 10.0;
    float d = march(ro, rd, prec, maxd);

    float li  = light_intensity;
    float refl_i = reflection_intensity;
    float refr_a = refraction_angle;
    float refr_i = refraction_intensity;
    float bii    = base_brightness;

    float b = bii;

    if (d < maxd)
    {
        vec3 p = ro + rd * d;
        vec3 n = calcNormal(p);

        b = li;

        vec3 reflRay = reflect(rd, n);
        vec3 refrRay = refract(rd, n, refr_a);

        // Approximate cube map look-ups using a 2D texture (equirectangular)
        vec2 envUV1 = vec2(atan(reflRay.z, reflRay.x) / m2pi + 0.5, acos(reflRay.y) / 3.14159);
        vec2 envUV2 = vec2(atan(refrRay.z, refrRay.x) / m2pi + 0.5, acos(refrRay.y) / 3.14159);
        vec3 cubeRefl = texture(iChannel0, envUV1).rgb * refl_i;
        vec3 cubeRefr = texture(iChannel0, envUV2).rgb * refr_i;

        col = (cubeRefl + cubeRefr + pow(b, 15.0)) * sphere_color;

        // Simple lighting terms (ported verbatim)
        vec3  lig = normalize(vec3(-0.6, 0.7, -0.5));
        float amb = clamp(0.5 + 0.5 * n.y, 0.0, 1.0);
        float dif = clamp(dot(n, lig), 0.0, 1.0);
        float bac = clamp(dot(n, normalize(vec3(-lig.x, 0.0, -lig.z))), 0.0, 1.0) * clamp(1.0 - p.y, 0.0, 1.0);
        float dom = smoothstep(-0.1, 0.1, reflRay.y);
        float fre = pow(clamp(1.0 + dot(n, rd), 0.0, 1.0), 2.0);
        float spe = pow(clamp(dot(reflect(rd, n), lig), 0.0, 1.0), specular_power);

        vec3 brdf = vec3(0.0);
        brdf += 1.20 * dif * vec3(1.00, 0.90, 0.60);
        brdf += 1.20 * spe * vec3(1.00, 0.90, 0.60) * dif;
        brdf += 0.30 * amb * vec3(0.50, 0.70, 1.00);
        brdf += 0.40 * dom * vec3(0.50, 0.70, 1.00);
        brdf += 0.30 * bac * vec3(0.25, 0.25, 0.25);
        brdf += 0.40 * fre * vec3(1.00, 1.00, 1.00);
        brdf += 0.02;

        col *= brdf;

        col = mix(col, displacement_color, 1.0 - exp(-0.0005 * d * d));

        // Blend with displacement colors
        col = mix(col, map(p).yzw * displacement_color, displacement_blend);
    }
    else
    {
        b += 0.1;
        // Environment fallback
        vec2 envUV = vec2(atan(rd.z, rd.x) / m2pi + 0.5, acos(rd.y) / 3.14159);
        col = texture(iChannel0, envUV).rgb * sphere_color;

        if (background_transparent)
        {
            alpha = 0.0;
        }
    }

    COLOR = vec4(col, alpha);
}
