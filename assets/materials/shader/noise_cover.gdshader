shader_type canvas_item;
render_mode unshaded;

#include "res://addons/shader_utils/canvas_group_blend.gdshaderinc"

uniform sampler2D noise_pattern;
uniform sampler2D noise_pattern2;
uniform vec2 scroll = vec2(0,0.2);
uniform vec2 scrol2 = vec2(0,0.5);
uniform sampler2D overlap_color_gradient;
uniform float color_intensity : hint_range(0.0, 5.0) = 2.0;
instance uniform vec2 noise_seed = vec2(0.0, 0.0);
uniform float threshold : hint_range(0.0, 1.0) = 0.2;
uniform float noise_alpha : hint_range(0.0, 1.0) = 1.0;

void fragment() {
	// --- 1. 生成特效颜色 ---
	vec2 seed_offset = noise_seed * 0.1;
	vec2 noise_uv = fract(UV + seed_offset + TIME * scroll);
	vec2 noise_uv2 = fract(UV - seed_offset + TIME * scrol2);

	vec4 noise1 = texture(noise_pattern, noise_uv);
	vec4 noise2 = texture(noise_pattern2, noise_uv2);
	vec4 final_noise = noise1 * noise2;

	float noise_brightness = final_noise.r;
	vec4 noise_color = texture(overlap_color_gradient, vec2(noise_brightness, 0.5));
	vec4 colored_noise = final_noise * noise_color * color_intensity;

	float fire_alpha = smoothstep(threshold, threshold + 0.1, noise_brightness) * noise_alpha;

	// 特效颜色现在是一个独立的变量，包含 RGB 和 Alpha
	vec4 effect = vec4(colored_noise.rgb, fire_alpha);

	// --- 2. 与背景混合 ---
	COLOR = blend_canvas_background(effect, SCREEN_UV);
}
