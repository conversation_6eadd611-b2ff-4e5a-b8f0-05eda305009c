shader_type canvas_item;


uniform vec4 color: source_color = vec4(0.41,0.64,0.78,1);

group_uniforms ring_shape;
uniform float ring_outer_radius : hint_range(0.0, 0.5, 0.001) = 0.35;
uniform float ring_inner_radius : hint_range(0.0, 0.5, 0.001) = 0.275;
uniform float ring_outer_fade_thickness : hint_range(0.0, 0.2, 0.001) = 0.01;
uniform float ring_inner_fade_thickness : hint_range(0.0, 1, 0.001) = 0.1;

group_uniforms animation;
uniform float animation_speed : hint_range(0.0, 10.0) = 1.0;
uniform float animation_magnitude : hint_range(0.0, 0.1, 0.001) = 0.01;

// Improved circle function with better anti-aliasing
float circle(vec2 position, float radius, float feather)
{
	// Calculate distance from center (0.5, 0.5)
	float dist = length(position - vec2(0.5));

	// Use a smoother step function with pixel-perfect feathering
	float pixel_size = length(fwidth(position));
	float feather_adjusted = max(feather, pixel_size * 2.0);

	return smoothstep(radius, radius + feather_adjusted, dist);
}

void fragment(){

	float outer = circle(vec2(UV.x, UV.y), ring_outer_radius, ring_outer_fade_thickness);

	float fade_effect = sin(TIME * animation_speed) * animation_magnitude;

	// Ensure the feathering doesn't become negative, which can cause rendering artifacts.
	float current_inner_fade = max(0.0001, ring_inner_fade_thickness - fade_effect);
	float inner = 1.0 - circle(vec2(UV.x, UV.y), ring_inner_radius, current_inner_fade);

	COLOR = color;
	COLOR.a -= outer + inner;

	// Pre-multiply alpha to prevent color bleeding on transparent areas.
	COLOR.rgb *= COLOR.a;

}