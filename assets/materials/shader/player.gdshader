shader_type canvas_item;
render_mode unshaded;

// --- 顶点拉伸效果 ---
// uniform
// bool - 用来从脚本中开启或关闭此效果。
uniform bool active = false;
// float - Y轴方向的拉伸强度。1.0为原样，1.2为放大20%。
uniform float stretch_y : hint_range(1.0, 2.0) = 1.2;
// float - X轴方向的拉伸强度。
uniform float stretch_x : hint_range(1.0, 2.0) = 1.1;
// float - 定义身体开始的Y轴位置(0.0=顶部, 1.0=底部)。
// 拉伸效果会作用于此分割线以上的顶点。
uniform float body_starts_at_y : hint_range(0.0, 1.0) = 0.5;

// --- 受击闪烁效果 ---
uniform vec4 flash_color : source_color = vec4(1.0, 1.0, 1.0, 1.0);
uniform float flash_modifier : hint_range(0.0, 1.0) = 0.0;


void vertex() {
	if (active) {
		// smoothstep可以创造一个平滑的过渡效果。
		// 我们用1.0-UV.y来反转坐标，使其更符合“高度”的直观认知(即底部为0，顶部为1)。
		float height = 1.0 - UV.y;
		float effect_start_height = 1.0 - body_starts_at_y;

		// 这个因子(factor)对于完全在“头部”区域的顶点将是1.0，
		// 在“身体”区域的将是0.0，在过渡区域的则是0到1之间的值。
		float stretch_factor = smoothstep(effect_start_height, effect_start_height + 0.1, height);

		if (stretch_factor > 0.0) {
			// 我们希望相对于分割线进行缩放，而不是图片的中心。
			// 首先，计算出分割线在Y轴上的顶点坐标。
			// 对于一个中心对齐的Sprite，UV.y=0.5对应于VERTEX.y=0。
			// 因此，轴心点的Y坐标是 (body_starts_at_y - 0.5) * (1.0 / TEXTURE_PIXEL_SIZE.y)。
			float pivot_y = (body_starts_at_y - 0.5) * (1.0 / TEXTURE_PIXEL_SIZE.y);

			// 应用拉伸，从轴心点开始缩放。
			VERTEX.y = pivot_y + (VERTEX.y - pivot_y) * mix(1.0, stretch_y, stretch_factor);
			// X轴方向则相对于中心线(VERTEX.x = 0)进行缩放。
			VERTEX.x *= mix(1.0, stretch_x, stretch_factor);
		}
	}
}

void fragment() {
	// 获取原始纹理颜色
	vec4 original_color = texture(TEXTURE, UV);

	// 使用mix函数将原始颜色与闪烁颜色根据修改器强度进行混合
	// 我们只混合RGB颜色通道，保持原始的Alpha透明度不变
	vec3 final_color = mix(original_color.rgb, flash_color.rgb, flash_modifier);

	// 设置最终的像素颜色
	COLOR = vec4(final_color, original_color.a);
}
