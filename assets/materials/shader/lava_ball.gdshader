shader_type canvas_item;

/* ---------------- 常量 & 宏 ---------------- */
#define saturate(oo) clamp(oo, 0.0, 1.0)

// ——质量相关（步进次数越大效果越好但更耗性能）
#define MarchSteps 8
// ——场景固定值
const vec3 EXP_POSITION = vec3(0.0); // 火球中心
const vec4 BACKGROUND   = vec4(0.0, 0.0, 0.0, 0);
const int  NoiseSteps   = 1;         // Turbulence 内叠加层数
const vec3 ANIMATION_VEC = vec3(0.0, -3.0, 0.5); // 基础运动方向

/* ---------------- 用户可调 Uniform ---------------- */
// 尺寸（球半径）
uniform float radius : hint_range(0.1, 10.0) = 2.0;
// 噪声强度 & 频率
uniform float noise_amplitude  : hint_range(0.0, 0.2) = 0.06;
uniform float noise_frequency  : hint_range(0.1, 10.0) = 4.0;
// 运动速度倍率
uniform float speed : hint_range(0.0, 5.0) = 1.0;
// 辉光强度
uniform float glow_intensity_min : hint_range(0.0, 20.0) = 4.0;
uniform float glow_intensity_max : hint_range(0.0, 20.0) = 6.0;
uniform float glow_flicker_speed : hint_range(0.1, 10.0) = 1.0;
// 颜色梯度
uniform vec4 color1 : source_color = vec4(1.0, 1.0, 1.0, 1.0);
uniform vec4 color2 : source_color = vec4(1.0, 0.8, 0.2, 1.0);
uniform vec4 color3 : source_color = vec4(1.0, 0.03, 0.0, 1.0);
uniform vec4 color4 : source_color = vec4(0.05, 0.02, 0.02, 1.0);

/* ---------------- Ashima 3D Simplex Noise ---------------- */
vec3  mod289(vec3  x){ return x - floor(x*(1.0/289.0))*289.0; }
vec4  mod289(vec4  x){ return x - floor(x*(1.0/289.0))*289.0; }
vec4  permute(vec4 x){ return mod289(((x*34.0)+1.0)*x);      }
vec4  taylorInvSqrt(vec4 r){ return 1.79284291400159 - 0.85373472095314 * r; }

float snoise(vec3 v){
	const vec2  C = vec2(1.0/6.0, 1.0/3.0);
	const vec4  D = vec4(0.0, 0.5, 1.0, 2.0);
	vec3 i  = floor(v + dot(v, C.yyy));
	vec3 x0 = v - i + dot(i, C.xxx);
	vec3 g = step(x0.yzx, x0.xyz);
	vec3 l = 1.0 - g;
	vec3 i1 = min(g.xyz, l.zxy);
	vec3 i2 = max(g.xyz, l.zxy);
	vec3 x1 = x0 - i1 + C.xxx;
	vec3 x2 = x0 - i2 + C.yyy;
	vec3 x3 = x0 - D.yyy;
	i = mod289(i);
	vec4 p = permute( permute( permute( i.z + vec4(0.0, i1.z, i2.z, 1.0))
	                      + i.y + vec4(0.0, i1.y, i2.y, 1.0))
	                      + i.x + vec4(0.0, i1.x, i2.x, 1.0) );
	float n_ = 0.142857142857; // 1/7
	vec3  ns = n_ * D.wyz - D.xzx;
	vec4 j  = p - 49.0 * floor(p * ns.z * ns.z);
	vec4 x_ = floor(j * ns.z);
	vec4 y_ = floor(j - 7.0 * x_);
	vec4 x = x_ * ns.x + ns.yyyy;
	vec4 y = y_ * ns.x + ns.yyyy;
	vec4 h = 1.0 - abs(x) - abs(y);
	vec4 b0 = vec4(x.xy, y.xy);
	vec4 b1 = vec4(x.zw, y.zw);
	vec4 s0 = floor(b0) * 2.0 + 1.0;
	vec4 s1 = floor(b1) * 2.0 + 1.0;
	vec4 sh = -step(h, vec4(0.0));
	vec4 a0 = b0.xzyw + s0.xzyw * sh.xxyy;
	vec4 a1 = b1.xzyw + s1.xzyw * sh.zzww;
	vec3 p0 = vec3(a0.xy, h.x);
	vec3 p1 = vec3(a0.zw, h.y);
	vec3 p2 = vec3(a1.xy, h.z);
	vec3 p3 = vec3(a1.zw, h.w);
	vec4 norm = taylorInvSqrt(vec4(dot(p0,p0), dot(p1,p1), dot(p2,p2), dot(p3,p3)));
	p0 *= norm.x; p1 *= norm.y; p2 *= norm.z; p3 *= norm.w;
	vec4 m = max(0.6 - vec4(dot(x0,x0), dot(x1,x1), dot(x2,x2), dot(x3,x3)), 0.0);
	m = m*m;
	return 42.0 * dot(m*m, vec4(dot(p0,x0), dot(p1,x1), dot(p2,x2), dot(p3,x3)));
}

/* ---------------- 功能函数 ---------------- */
float Turbulence(vec3 p, float minF, float maxF, float qWidth){
	float val = 0.0;
	float cutoff = clamp(0.5/qWidth, 0.0, maxF);
	float fOut  = minF;
	for(int i = NoiseSteps; i >= 0; i--){
		if (fOut >= 0.5 * cutoff) break;
		fOut *= 2.0;
		val += abs(snoise(p * fOut)) / fOut;
	}
	float fade = clamp(2.0 * (cutoff - fOut) / cutoff, 0.0, 1.0);
	val += fade * abs(snoise(p * fOut)) / fOut;
	return 1.0 - val;
}

float sphere_dist(vec3 pos){
	return length(pos - EXP_POSITION) - radius;
}

vec4 shade(float dist){
	// 使用三角波函数，让 glow_intensity 在 min/max 间线性地来回变化
	float time_factor = 1.0 - abs(fract(TIME * glow_flicker_speed * 0.5) * 2.0 - 1.0);
	float glow_intensity = mix(glow_intensity_min, glow_intensity_max, time_factor);

	float c1 = saturate(dist*5.0 + 0.5);
	// float c1 = saturate(dist * glow_intensity + 0.5);
	float c2 = saturate(glow_intensity * dist - 0.1);
	float c3 = saturate(dist*3.4 - 0.5);

	vec4 a = mix(color1, color2, c1);
	vec4 b = mix(a,      color3, c2);
	return mix(b,         color4, c3);
}

/* ------ 核心渲染：噪声 + 距离场 ------ */
float render_scene(vec3 pos, out float dist){
	float noise = Turbulence(pos * noise_frequency + ANIMATION_VEC * (TIME * speed),
	                         0.1, 1.5, 0.03) * noise_amplitude;
	noise = saturate(abs(noise));
	dist  = sphere_dist(pos) - noise;
	return noise;
}

/* ------ Ray Marching ------ */
vec4 march(vec3 ray_orig, vec3 ray_dir){
	vec3 p = ray_orig;
	float d, disp;
	for(int i = MarchSteps; i >= 0; --i){
		disp = render_scene(p, d);
		if (d < 0.05) break;
		p += ray_dir * d;
	}
	return mix(shade(disp), BACKGROUND, float(d >= 0.5));
}

/* ------ 球体相交 ------ */
bool intersect_sphere(vec3 ro, vec3 rd, vec3 center, float r, out vec3 hit_pos){
	vec3  rel = ro - center;
	float b   = dot(rel, rd);
	float c   = dot(rel, rel) - r*r;
	float d   = b*b - c;
	hit_pos    = ro + rd * (-b - sqrt(d));
	return d >= 0.0;
}

/* ---------- fragment 主函数 ---------- */
void fragment(){
	// 贴图尺寸 & 归一化坐标 [-1,1]
	vec2 tex_size = 1.0 / TEXTURE_PIXEL_SIZE;
	vec2 p = UV * 2.0 - 1.0;
	p.x *= tex_size.x / tex_size.y;

	// 固定摄像机参数
	float zoom = 5.0;
	vec3 ro = vec3(0.0, 0.0, zoom);
	vec3 ww = normalize(-ro);                 // -Z 方向
	vec3 uu = normalize(cross(vec3(0.0,1.0,0.0), ww));
	vec3 vv = normalize(cross(ww, uu));
	vec3 rd = normalize(p.x*uu + p.y*vv + 1.5*ww);

	// 场景求交 & 体渲染
	vec4 col = BACKGROUND;
	vec3 origin;
	if (intersect_sphere(ro, rd, EXP_POSITION, radius + noise_amplitude * 6.0, origin)){
		col = march(origin, rd);
	}
	COLOR = col;
}