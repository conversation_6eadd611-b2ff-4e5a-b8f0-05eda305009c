shader_type canvas_item;

// 将 Shadertoy 代码转换为 Godot 4.4 CanvasItem Shader
// =====================================================
// 常量（如无其他用途可省略）
// -----------------------------------------------------

// 新增：分别控制 X、Y 轴旋转（弧度）
uniform float u_rot_x : hint_range(-3.1416, 3.1416, 0.01) = 0.0; // 绕 X 轴
uniform float u_rot_y : hint_range(-3.1416, 3.1416, 0.01) = 0.0; // 绕 Y 轴

// 透视强度：0 = 完全正交，1 = 完全透视
uniform float u_persp : hint_range(0.0, 1.0, 0.01) = 1.0;

// 正方体整体缩放倍数（>0）。与纹理 UV 同步，受节点拉伸影响。
uniform float u_zoom : hint_range(0.1, 10.0, 0.1) = 3.0;

// 边框粗细（像素）。保证在节点拉伸或缩放时保持恒定像素宽度。
uniform float u_edge_px : hint_range(0.5, 5.0, 0.1) = 1.0;

// 新增：边框颜色
uniform vec4 u_edge_color : source_color = vec4(0.0, 1.0, 1.0, 1.0);

// 新增：立方体面颜色（含透明度）。若 alpha = 0 则保持仅线框效果。
uniform vec4 u_face_color : source_color = vec4(0.0, 0.0, 0.0, 0.0);

// 新增：顶面颜色（含透明度）。若 alpha = 0 则使用 u_face_color。
uniform vec4 u_top_color  : source_color = vec4(0.0, 0.0, 0.0, 0.0);

// 新增：顶面内圈颜色与大小控制
uniform vec4  u_top_inner_color  : source_color = vec4(1.0, 0.0, 0.0, 1.0);
uniform float u_top_inner_ratio : hint_range(0.0, 0.49, 0.01) = 0.2; // 0 = 无内圈，0.49 接近边缘
// 如需进一步扩展可自行添加 Z 轴或自定义旋转轴
// -----------------------------------------------------
// 任意轴旋转矩阵
mat4 rotate_mat(vec3 u, float theta) {
    float c = cos(theta);
    float s = sin(theta);
    u = normalize(u);

    vec4 c0 = vec4(u.x * u.x * (1.0 - c) + c,
                   u.x * u.y * (1.0 - c) + u.z * s,
                   u.x * u.z * (1.0 - c) - u.y * s,
                   0.0);

    vec4 c1 = vec4(u.x * u.y * (1.0 - c) - u.z * s,
                   u.y * u.y * (1.0 - c) + c,
                   u.y * u.z * (1.0 - c) + u.x * s,
                   0.0);

    vec4 c2 = vec4(u.z * u.x * (1.0 - c) + u.y * s,
                   u.z * u.y * (1.0 - c) - u.x * s,
                   u.z * u.z * (1.0 - c) + c,
                   0.0);

    vec4 c3 = vec4(0.0, 0.0, 0.0, 1.0);

    return mat4(c0, c1, c2, c3);
}

// -----------------------------------------------------
// 投影函数
vec2 point_orthographic(vec3 point) {
    return point.xy;
}

vec2 point_perspective(vec3 point) {
    vec3 project_point = vec3(0.0, 0.0, 15.0);
    vec3 view_plane    = vec3(0.0, 0.0, 5.0);

    float aspect = (view_plane.z - project_point.z) / (point.z - project_point.z);
    return vec2(
        aspect * (point.x - project_point.x) + project_point.x,
        aspect * (point.y - project_point.y) + project_point.y
    );
}

// 计算 uv 到线段 (p0–p1) 的距离并返回平滑插值
float project_line(vec3 p0, vec3 p1, vec2 uv) {
    // 根据用户设置在正交与透视之间插值
    vec2 pp0 = mix(point_orthographic(p0), point_perspective(p0), u_persp);
    vec2 pp1 = mix(point_orthographic(p1), point_perspective(p1), u_persp);

    vec2 dir = normalize(pp1 - pp0);
    float len = distance(pp1, pp0);

    float cos_theta = dir.x;
    float sin_theta = dir.y;
    mat2 rotate2d = mat2(vec2(cos_theta,  sin_theta),
                         vec2(-sin_theta, cos_theta));

    vec2 rotated = (uv - pp0) * rotate2d;
    float min_x  = clamp(rotated.x, 0.0, len);
    float d      = distance(vec2(min_x, 0.0), rotated);

    // 利用屏幕导数保证边框固定像素宽度
    float st_per_px = max(length(dFdx(uv)), length(dFdy(uv))); // 一个像素对应的 st 长度
    float edge_thickness = st_per_px * u_edge_px;

    return smoothstep(edge_thickness, 0.0, d);
}

// -----------------------------------------------------
// 2D 几何辅助（用于面填充）
float cross2d(vec2 a, vec2 b) {
    return a.x * b.y - a.y * b.x;
}

float insideTri(vec2 p, vec2 a, vec2 b, vec2 c) {
    float s1 = cross2d(b - a, p - a);
    float s2 = cross2d(c - b, p - b);
    float s3 = cross2d(a - c, p - c);
    float pos = step(0.0, s1) * step(0.0, s2) * step(0.0, s3);
    float neg = step(0.0, -s1) * step(0.0, -s2) * step(0.0, -s3);
    return clamp(pos + neg, 0.0, 1.0);
}

float insideQuad(vec2 p, vec2 a, vec2 b, vec2 c, vec2 d) {
    return clamp(insideTri(p, a, b, c) + insideTri(p, a, c, d), 0.0, 1.0);
}
// -----------------------------------------------------
// 渲染线框立方体
vec4 render_line_cube(vec2 st) {
    float size = 3.0;

    // 立方体八顶点 (单位立方体)
    vec3 vertices[8] = vec3[](
        vec3( 1.0,  1.0,  1.0),
        vec3(-1.0,  1.0,  1.0),
        vec3(-1.0,  1.0, -1.0),
        vec3( 1.0,  1.0, -1.0),
        vec3( 1.0, -1.0, -1.0),
        vec3( 1.0, -1.0,  1.0),
        vec3(-1.0, -1.0,  1.0),
        vec3(-1.0, -1.0, -1.0)
    );

    float pct = 0.0;
    // 先绕 X 再绕 Y
    mat4 rot_x = rotate_mat(vec3(1.0, 0.0, 0.0), u_rot_x);
    mat4 rot_y = rotate_mat(vec3(0.0, 1.0, 0.0), u_rot_y);
    mat4 rot   = rot_y * rot_x;

    // 顶点旋转 & 缩放
    vec2 proj[8];
    for (int i = 0; i < 8; i++) {
        vertices[i] = (rot * vec4(vertices[i] * size, 0.0)).xyz;

        // 保存每个顶点的投影坐标，供后续计算
        proj[i] = mix(point_orthographic(vertices[i]), point_perspective(vertices[i]), u_persp);
    }

    // -------------------------------------------------
    // 计算各面的可见性（1 = 面朝向摄像机）
    vec3 view_dir = vec3(0.0, 0.0, -1.0);
    vec3 n_top    = (rot * vec4(0.0,  1.0,  0.0, 0.0)).xyz;
    vec3 n_bottom = (rot * vec4(0.0, -1.0,  0.0, 0.0)).xyz;
    vec3 n_front  = (rot * vec4(0.0,  0.0,  1.0, 0.0)).xyz;
    vec3 n_back   = (rot * vec4(0.0,  0.0, -1.0, 0.0)).xyz;
    vec3 n_right  = (rot * vec4(1.0,  0.0,  0.0, 0.0)).xyz;
    vec3 n_left   = (rot * vec4(-1.0, 0.0,  0.0, 0.0)).xyz;

    float vis_top    = step(0.0, -dot(n_top,    view_dir));
    float vis_bottom = step(0.0, -dot(n_bottom, view_dir));
    float vis_front  = step(0.0, -dot(n_front,  view_dir));
    float vis_back   = step(0.0, -dot(n_back,   view_dir));
    float vis_right  = step(0.0, -dot(n_right,  view_dir));
    float vis_left   = step(0.0, -dot(n_left,   view_dir));

    // 顶面四条边
    pct += project_line(vertices[0], vertices[1], st) * clamp(vis_top + vis_front, 0.0, 1.0);
    pct += project_line(vertices[1], vertices[2], st) * clamp(vis_top + vis_left, 0.0, 1.0);
    pct += project_line(vertices[2], vertices[3], st) * clamp(vis_top + vis_back, 0.0, 1.0);
    pct += project_line(vertices[3], vertices[0], st) * clamp(vis_top + vis_right, 0.0, 1.0);

    // 垂直四条边
    pct += project_line(vertices[0], vertices[5], st) * clamp(vis_front + vis_right, 0.0, 1.0);
    pct += project_line(vertices[1], vertices[6], st) * clamp(vis_front + vis_left, 0.0, 1.0);
    pct += project_line(vertices[2], vertices[7], st) * clamp(vis_back  + vis_left, 0.0, 1.0);
    pct += project_line(vertices[3], vertices[4], st) * clamp(vis_back  + vis_right,0.0, 1.0);

    // 底面四条边
    pct += project_line(vertices[5], vertices[6], st) * clamp(vis_bottom + vis_front, 0.0, 1.0);
    pct += project_line(vertices[6], vertices[7], st) * clamp(vis_bottom + vis_left, 0.0, 1.0);
    pct += project_line(vertices[7], vertices[4], st) * clamp(vis_bottom + vis_back, 0.0, 1.0);
    pct += project_line(vertices[4], vertices[5], st) * clamp(vis_bottom + vis_right,0.0, 1.0);

    // -------------------------------------------------
    // 精确计算立方体面投影填充
    float face_mask = 0.0;
    // 顶面单独记录
    float mask_top = insideQuad(st, proj[0], proj[1], proj[2], proj[3]) * vis_top;
    face_mask += mask_top;
    // 底面
    face_mask += insideQuad(st, proj[5], proj[6], proj[7], proj[4]);
    // 左、右
    face_mask += insideQuad(st, proj[1], proj[6], proj[7], proj[2]);
    face_mask += insideQuad(st, proj[0], proj[5], proj[4], proj[3]);
    // 前、后
    face_mask += insideQuad(st, proj[0], proj[1], proj[6], proj[5]);
    face_mask += insideQuad(st, proj[3], proj[2], proj[7], proj[4]);
    face_mask = clamp(face_mask, 0.0, 1.0);

    // -------------------------------------------------
    // 顶面内圈检测（使用平行四边形参数化）
    float mask_top_inner = 0.0;
    if (mask_top > 0.0) {
        vec2 e1 = proj[1] - proj[0]; // AB
        vec2 e2 = proj[3] - proj[0]; // AD
        float det = e1.x * e2.y - e1.y * e2.x;
        if (abs(det) > 1e-4) {
            vec2 p_local = st - proj[0];
            float a = (p_local.x * e2.y - p_local.y * e2.x) / det;
            float b = (e1.x * p_local.y - e1.y * p_local.x) / det;

            float margin = u_top_inner_ratio;
            float inside_a = step(margin, a) * (1.0 - step(1.0 - margin, a));
            float inside_b = step(margin, b) * (1.0 - step(1.0 - margin, b));
            mask_top_inner = inside_a * inside_b * vis_top;
        }
    }

    // 根据 mask_top 选择顶面颜色或通用颜色
    vec3  face_rgb_base = u_face_color.rgb;
    float face_a_base   = u_face_color.a;

    vec3  face_rgb_top  = u_top_color.rgb;
    float face_a_top    = u_top_color.a;

    // 先应用顶面外圈颜色
    vec3  face_rgb = mix(face_rgb_base, face_rgb_top, mask_top);
    float face_a   = mix(face_a_base,  face_a_top,  mask_top);

    // 再覆盖顶面内圈颜色
    face_rgb = mix(face_rgb, u_top_inner_color.rgb, mask_top_inner);
    face_a   = mix(face_a,  u_top_inner_color.a,   mask_top_inner);

    // 仅在立方体面区域内显示
    vec3 line_rgb   = u_edge_color.rgb;
    face_rgb *= face_mask;
    face_a  *= face_mask;
    float line_a    = u_edge_color.a;

    // 先填充面，再叠加线框
    vec3 result_rgb = face_rgb;
    float result_a  = face_a;

    // 将线框颜色混入
    result_rgb = mix(result_rgb, line_rgb, pct);
    result_a   = mix(result_a,  line_a,  pct);

    return vec4(result_rgb, result_a);
}

// =====================================================
// Fragment 主入口
void fragment() {
    // 使用纹理 UV 坐标，使其随节点拉伸缩放而变化
    vec2 st = vec2((UV.x - 0.5) * 2.0, (0.5 - UV.y) * 2.0); // Y 轴向上

    // 直接按比例缩放，保持与节点拉伸一致
    st *= u_zoom;

    vec4 color = render_line_cube(st);
    COLOR = color;
}
