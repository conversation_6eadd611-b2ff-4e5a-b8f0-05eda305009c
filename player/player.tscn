[gd_scene load_steps=22 format=3 uid="uid://dvy2puti8lr3d"]

[ext_resource type="Script" uid="uid://dr2fdbpwu2yap" path="res://player/player_manager.gd" id="1_ev8v4"]
[ext_resource type="Texture2D" uid="uid://djhhef5jteof0" path="res://assets/imgs/player/CP002AB.png" id="1_sxsrd"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="2_iylwk"]
[ext_resource type="Shader" uid="uid://c10o1s1sxkljo" path="res://assets/materials/shader/player.gdshader" id="3_2c4n1"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="3_ev8v4"]
[ext_resource type="Script" uid="uid://cet5j0wnf3hnt" path="res://player/muzzle.gd" id="3_vhck3"]
[ext_resource type="Script" uid="uid://dvjh7sib4d3po" path="res://attribute/hp_attribute.gd" id="4_vhck3"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="5_2c4n1"]
[ext_resource type="Script" uid="uid://qrryd8xa62jp" path="res://relic/RelicManager.gd" id="6_oo3uv"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_ev8v4"]
height = 40.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_oo3uv"]
shader = ExtResource("3_2c4n1")
shader_parameter/active = true
shader_parameter/stretch_y = 1.26
shader_parameter/stretch_x = 1.23
shader_parameter/body_starts_at_y = 0.5
shader_parameter/flash_color = Color(1, 1, 1, 1)
shader_parameter/flash_modifier = 0.0

[sub_resource type="AtlasTexture" id="AtlasTexture_vhck3"]
atlas = ExtResource("1_sxsrd")
region = Rect2(90, 120, 45, 60)

[sub_resource type="AtlasTexture" id="AtlasTexture_rdx4y"]
atlas = ExtResource("1_sxsrd")
region = Rect2(0, 120, 45, 60)

[sub_resource type="AtlasTexture" id="AtlasTexture_wv1mm"]
atlas = ExtResource("1_sxsrd")
region = Rect2(45, 120, 45, 60)

[sub_resource type="AtlasTexture" id="AtlasTexture_binfn"]
atlas = ExtResource("1_sxsrd")
region = Rect2(90, 120, 45, 60)

[sub_resource type="AtlasTexture" id="AtlasTexture_klv5p"]
atlas = ExtResource("1_sxsrd")
region = Rect2(135, 120, 45, 60)

[sub_resource type="SpriteFrames" id="SpriteFrames_iylwk"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_vhck3")
}],
"loop": true,
"name": &"idle",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_rdx4y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wv1mm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_binfn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_klv5p")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="Resource" id="Resource_pm3ni"]
script = ExtResource("3_ev8v4")
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_jscy8"]
script = ExtResource("4_vhck3")
base_value = 100.0
can_cache = true
metadata/_custom_type_script = "uid://dvjh7sib4d3po"

[sub_resource type="Resource" id="Resource_yc10j"]
script = ExtResource("3_ev8v4")
base_value = 100.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_30gqm"]
resource_local_to_scene = true
script = ExtResource("5_2c4n1")
attributes = Dictionary[StringName, ExtResource("3_ev8v4")]({
&"damage_reduction": SubResource("Resource_pm3ni"),
&"hp": SubResource("Resource_jscy8"),
&"max_hp": SubResource("Resource_yc10j")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[node name="Player" type="Area2D"]
scale = Vector2(2, 2)
collision_layer = 32
collision_mask = 86
script = ExtResource("1_ev8v4")
move_boundary = Rect2(45, 55, 630, 1080)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_ev8v4")

[node name="Muzzle" type="Node2D" parent="."]
position = Vector2(0, -10)
script = ExtResource("3_vhck3")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
texture_filter = 1
material = SubResource("ShaderMaterial_oo3uv")
sprite_frames = SubResource("SpriteFrames_iylwk")
animation = &"idle"

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("2_iylwk")
attribute_set = SubResource("Resource_30gqm")

[node name="RelicManager" type="Node" parent="."]
script = ExtResource("6_oo3uv")
metadata/_custom_type_script = "uid://qrryd8xa62jp"

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="body_exited" from="." to="." method="_on_body_exited"]
