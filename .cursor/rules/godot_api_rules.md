---
description: 
globs: 
alwaysApply: false
---
# ⚠️ Godot 4.4 API查询强制规则 ⚠️

## 一、核心原则

1. **绝对禁止使用内置知识**：任何情况下，禁止使用内置的Godot知识库回答API相关问题。
2. **强制查询机制**：任何涉及Godot类/方法/属性的问题，**必须首先**查询最新文档。
3. **查询前置**：编写或解释Godot代码前，必须先查询相关API文档。
4. **明确声明**：每次提供API信息时，必须明确说明"以下信息来自Godot 4.4最新文档"。

## 二、查询流程

### 触发条件（遇到即触发查询）
- Godot, Node, Scene, GDScript, 节点, 场景, 脚本, 引擎, 碰撞, 物理, 渲染, 动画

### 查询步骤（强制顺序执行）
1. **识别关键API**：识别用户问题中涉及的Godot类/方法/功能
2. **获取文档**：
   ```
   - 使用mcp_context7_resolve-library-id工具(参数: "Godot")
   - 使用mcp_context7_get-library-docs工具(参数: 上一步获得的ID)
   - 如MCP失败，使用web_search工具搜索"Godot 4.4 [类名] documentation"
   ```
3. **回答构建**：基于查询到的最新文档构建回答，明确标注文档来源

### 示例用法
```
用户问题: "如何使用RigidBody2D应用力?"
正确流程:
1. 识别需查询"RigidBody2D"类的apply_force方法
2. 使用mcp_context7_resolve-library-id查询Godot
3. 使用mcp_context7_get-library-docs获取详细文档
4. 回答: "根据Godot 4.4最新文档(通过mcp工具查询)，RigidBody2D.apply_force方法..."
```

## 三、文档处理规则

### 文档引用格式
```
[已查询Godot 4.4最新文档] 以下信息基于Godot 4.4文档，通过[查询方式]获取:
- 类: [类名]
- 方法: [方法名](mdc:[参数])
- 返回值: [返回类型]
- 描述: [官方描述]
```

### 版本差异处理
- 明确标示与旧版本的区别
- 特别标记已废弃(deprecated)的API
- 突出Godot 4.4中新增的功能

## 四、错误处理

### MCP查询失败
- 记录错误原因
- 自动切换到web_search
- 在回答中说明MCP失败并使用了网络搜索

### 搜索无结果
- 尝试搜索父类或相关类
- 如无法获取，明确告知用户无法找到最新文档
- 绝不使用内置知识作为替代

## 五、自我纠错机制

如发现未查询文档即回答Godot问题:
1. 停止当前回答
2. 道歉并说明将重新查询最新文档
3. 执行文档查询
4. 基于最新文档重新构建回答




