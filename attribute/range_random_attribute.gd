@tool
class_name RangeRandomAttribute extends Attribute

@export var min_value_source: String
@export var max_value_source: String

func get_value(consume_buffs: bool = false, consume_buffs_dependencies: bool = false) -> float:
	return super.get_value(consume_buffs, consume_buffs_dependencies)


func post_attribute_value_changed(_value: float) -> float:
	return _value


func custom_compute(operated_value: float, _compute_params: Array[Attribute]) -> float:
	return base_value + randf_range(_compute_params[0].get_value(), _compute_params[1].get_value())


## 属性依赖列表
## @ return: 返回依赖属性的名称数组
func derived_from() -> Array[String]:
	return [
		min_value_source,
		max_value_source
	]
