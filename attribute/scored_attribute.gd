@tool
class_name ScoredAttribute
extends Attribute

## 每分增长值
@export var growth_per_score: float = 0.0
@export var operate_type: AttributeModifier.OperationType = AttributeModifier.OperationType.ADD
@export var max_value: float = 9999.0
@export var min_value: float = -9999.0


func custom_compute(operated_value: float, _compute_params: Array[Attribute]) -> float:
	var diff = (GameManager.score/10.0) * growth_per_score
	match operate_type:
		AttributeModifier.OperationType.ADD: return clampf(base_value + diff, min_value, max_value)
		AttributeModifier.OperationType.SUB: return clampf(base_value - diff, min_value, max_value)
		AttributeModifier.OperationType.MULT: return clampf(base_value * diff, min_value, max_value)
		AttributeModifier.OperationType.DIVIDE: return 0.0 if is_zero_approx(diff) else clampf(base_value / diff, min_value, max_value)

		AttributeModifier.OperationType.SET:
			pass
		AttributeModifier.OperationType.PERCENTAGE:
			pass
	return 0.0
