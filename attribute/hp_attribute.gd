@tool
class_name HpAtt<PERSON>bute extends Attribute

func post_attribute_value_changed(_value: float) -> float:
	var max_hp_attribute = attribute_set.find_attribute("max_hp")
	if is_instance_valid(max_hp_attribute):
		_value = clamp(_value, 0.0, max_hp_attribute.get_value())
	return _value


func custom_compute(operated_value: float, _compute_params: Array[Attribute]) -> float:
	var max_hp_attribute = _compute_params[0]
	return clamp(operated_value, 0.0, max_hp_attribute.get_value())


## 属性依赖列表
## @ return: 返回依赖属性的名称数组
func derived_from() -> Array[String]:
	return [
		"max_hp"
	]
