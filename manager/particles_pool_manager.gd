extends Node

enum ParticleType {
	NORMAL_DIE_JUMP,
	NORMAL_DIE,
	ENEMY_ATTACK,
	POISON_SPLASH,
	POISON_PARTICLE,
	SHOCK,
}

# 在这里写死所有可用粒子类型和对应 PackedScene
var PARTICLE_SCENES: Dictionary = {
	ParticleType.NORMAL_DIE_JUMP: load("res://enemy/effect/enemy_normal_die_jump.tscn"),
	ParticleType.NORMAL_DIE: load("res://enemy/effect/enemy_normal_die.tscn"),
	ParticleType.ENEMY_ATTACK: load("res://enemy/effect/particles_enemy_attack.tscn"),
	ParticleType.POISON_SPLASH: load("res://ball/effect/poison_effect/poison_splash.tscn"),
	ParticleType.POISON_PARTICLE: load("res://ball/effect/poison_effect/poison_particle.tscn"),
	ParticleType.SHOCK: load("res://ball/effect/shock_effect/shock.tscn"),
}

const PARTICLE_INITIAL_COUNTS: Dictionary = {
	ParticleType.NORMAL_DIE_JUMP: 5,
	ParticleType.NORMAL_DIE: 5,
}

var _pools: Dictionary = {}


func _ready() -> void:
	for p_type in PARTICLE_SCENES:
		_pools[p_type] = []
		var initial_count = PARTICLE_INITIAL_COUNTS.get(p_type, 0)
		for i in range(initial_count):
			var particle_node: Node = _create_new_particle(p_type)
			_pools[p_type].push_back(particle_node)


func get_particle(type: ParticleType, global_pos: Vector2 = Vector2.ZERO, root: Node = null) -> Node:
	if not PARTICLE_SCENES.has(type):
		push_error("Unknown particle type: %s" % str(type))
		return null

	if not _pools.has(type):
		_pools[type] = []

	var pool: Array = _pools[type]
	var particle_node: Node

	if not pool.is_empty():
		particle_node = pool.pop_front()
	else:
		particle_node = _create_new_particle(type)

	# 确保在添加到新父节点前，粒子节点没有父节点
	if particle_node.get_parent():
		particle_node.get_parent().remove_child(particle_node)

	if root:
		root.add_child(particle_node)
		particle_node.global_position = global_pos
	else:
		get_tree().current_scene.add_child(particle_node)
		particle_node.global_position = global_pos

	if particle_node.has_method("play"):
		# 延迟播放粒子效果以避免物理引擎冲突
		particle_node.call_deferred("play")

	return particle_node


func _create_new_particle(type: ParticleType) -> Node:
	var particle_node = PARTICLE_SCENES[type].instantiate()
	particle_node.set_meta("type", type)

	if not particle_node.has_signal("finished"):
		var key_name: String = "UNKNOWN"
		for key in ParticleType.keys():
			if ParticleType[key] == type:
				key_name = key
				break
		push_warning("Particle scene for type '%s' does not have a 'finished' signal on its root. It may not be pooled correctly." % key_name)
	else:
		particle_node.finished.connect(recycle_particle.bind(particle_node))
	return particle_node


func recycle_particle(particle_node: Node) -> void:
	var type: ParticleType = particle_node.get_meta("type")
	if particle_node.get_parent():
		particle_node.get_parent().remove_child(particle_node)

	# 确保池中有这个类型的数组
	if not _pools.has(type):
		_pools[type] = []

	# 直接使用池中的数组引用
	_pools[type].push_back(particle_node)
