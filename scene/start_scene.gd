extends Node

@onready var swirl_text:TextureRect = $CanvasLayer/jinhua

# 用于时间累积，驱动手动扭曲进度
var _elapsed_time:float = 0.0

func _ready() -> void:
	init_tan_and_qiu()
	init_balls()

# 每帧更新旋涡进度（-1 ~ 1，越接近 0 速度越慢）
func _process(delta: float) -> void:
	process_text_swirl(delta)

func process_text_swirl(delta: float) -> void:
	_elapsed_time += delta*0.5
	# 基础正弦波 [-1, 1]
	var base_value: float = sin(_elapsed_time)
	# 调整速度：在 0 附近更慢 -> 使用平方保持符号或立方：value * abs(value)
	var progress: float = base_value * pow(abs(base_value), 4) # 也可用 pow(base_value, 3)
	var material := swirl_text.material
	if material is ShaderMaterial:
		material.set_shader_parameter("override_twist_progress", true)
		material.set_shader_parameter("twist_progress_override", progress)


func init_tan_and_qiu() -> void:
	var t1:RigidBody2D = $CanvasLayer/Control/tan
	var t2:RigidBody2D = $CanvasLayer/Control/qiu
	t1.linear_velocity = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized() * randf_range(10, 20)
	t2.linear_velocity = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized() * randf_range(10, 20)


func init_balls() -> void:
	var balls:Node = $Balls
	for ball: RigidBody2D in balls.get_children():
		ball.global_position = Vector2(randf_range(20, 700), randf_range(20, 1260))
		ball.linear_velocity = Vector2(randf_range(-1, 1), randf_range(-1, 1)).normalized() * randf_range(400, 800)
