[gd_scene load_steps=19 format=3 uid="uid://c3kcv0qt7espx"]

[ext_resource type="Shader" uid="uid://bksc706qvtyl0" path="res://assets/materials/shader/comos_bg.gdshader" id="1_3tgnh"]
[ext_resource type="Script" uid="uid://bd7ybt2celdsi" path="res://scene/start_scene.gd" id="1_i331m"]
[ext_resource type="Texture2D" uid="uid://cb6ee8c64ceqw" path="res://assets/imgs/bgs/Blue_Nebula_01-1024x1024.png" id="2_u7rsi"]
[ext_resource type="PackedScene" uid="uid://bnjnk8a884ewg" path="res://ball/prefab/fire_ball.tscn" id="4_2l3yw"]
[ext_resource type="Shader" uid="uid://c47dlycvk033i" path="res://assets/materials/shader/vrotex.gdshader" id="4_sg87u"]
[ext_resource type="PackedScene" uid="uid://bh7jabuqr1upf" path="res://ball/prefab/blood_ball.tscn" id="5_2525w"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_i331m"]
shader = ExtResource("1_3tgnh")
shader_parameter/OCTAVE = 7
shader_parameter/timescale = 1.0
shader_parameter/CLOUD1_COL = Color(0.41, 0.64, 0.78, 0.4)
shader_parameter/CLOUD2_COL = Color(0.99, 0.79, 0.46, 0.2)
shader_parameter/CLOUD3_COL = Color(0.81, 0.31, 0.59, 1)
shader_parameter/CLOUD4_COL = Color(0.27, 0.15, 0.33, 1)
shader_parameter/SPACE = Color(0.09, 0.06, 0.28, 0.3)
shader_parameter/zoomScale = 6.0
shader_parameter/size = 10.0
shader_parameter/starscale = 25.765
shader_parameter/prob = 0.999
shader_parameter/small_star_prob = 0.991

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_6vkx6"]
friction = 0.0
bounce = 1.0

[sub_resource type="ViewportTexture" id="ViewportTexture_sg87u"]
viewport_path = NodePath("CanvasLayer/jinhua/SubViewport")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_2525w"]
resource_local_to_scene = true
shader = ExtResource("4_sg87u")
shader_parameter/portal_texture = SubResource("ViewportTexture_sg87u")
shader_parameter/portal_tint = Color(1, 1, 1, 1)
shader_parameter/vortex_effect_radius = 1.0
shader_parameter/twist_strength = 7.0
shader_parameter/pulsation_speed = 0.0
shader_parameter/breath_magnitude = -1.86265e-08
shader_parameter/override_twist_progress = true
shader_parameter/twist_progress_override = 0.0
shader_parameter/overall_rotation_speed = -4.4704e-08
shader_parameter/texture_scroll_speed_x = -2.2352e-08
shader_parameter/texture_scroll_speed_y = -2.2352e-08
shader_parameter/edge_softness = 0.5

[sub_resource type="ViewportTexture" id="ViewportTexture_6vkx6"]
viewport_path = NodePath("CanvasLayer/jinhua/SubViewport")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_2l3yw"]

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_sg87u"]
friction = 0.0
bounce = 0.4

[sub_resource type="CircleShape2D" id="CircleShape2D_i331m"]
radius = 100.125

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_2l3yw"]
friction = 0.0
bounce = 0.5

[sub_resource type="CircleShape2D" id="CircleShape2D_0kdh5"]
radius = 104.043

[sub_resource type="PhysicsMaterial" id="PhysicsMaterial_0kdh5"]
friction = 0.0
bounce = 0.7

[sub_resource type="ConcavePolygonShape2D" id="ConcavePolygonShape2D_0kdh5"]
segments = PackedVector2Array(0, 0, 250, 0, 0, 150, 250, 150, 0, 0, 0, 150, 250, 0, 250, 150)

[node name="StartScene" type="Node"]
script = ExtResource("1_i331m")

[node name="Bg" type="Node" parent="."]

[node name="Img" type="Sprite2D" parent="Bg"]
visible = false
material = SubResource("ShaderMaterial_i331m")
position = Vector2(337, 641)
scale = Vector2(2.25928, 2.25928)
texture = ExtResource("2_u7rsi")

[node name="StaticBody2D" type="StaticBody2D" parent="Bg"]
collision_layer = 1073741824
collision_mask = 1073741824
physics_material_override = SubResource("PhysicsMaterial_6vkx6")

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="Bg/StaticBody2D"]
polygon = PackedVector2Array(0, 0, 0, 1280, -20, 1280, -20, -20, 740, -20, 740, 1300, -20, 1300, -20, 1280, 720, 1280, 720, 0, 0.01, 0)

[node name="Balls" type="Node" parent="."]

[node name="FireBall" parent="Balls" instance=ExtResource("4_2l3yw")]
collision_layer = 0
collision_mask = 1073741824
acceleration_multiplier = 0.0

[node name="BloodBall" parent="Balls" instance=ExtResource("5_2525w")]
collision_layer = 0
collision_mask = 1073741824
acceleration_multiplier = 0.0

[node name="CanvasLayer" type="CanvasLayer" parent="."]

[node name="jinhua" type="TextureRect" parent="CanvasLayer"]
material = SubResource("ShaderMaterial_2525w")
anchors_preset = -1
anchor_left = 0.5
anchor_right = 0.5
offset_left = -3.0
offset_top = 58.0
offset_right = 387.0
offset_bottom = 288.0
grow_horizontal = 2
scale = Vector2(0.8, 0.8)
size_flags_horizontal = 0
size_flags_vertical = 4
texture = SubResource("ViewportTexture_6vkx6")

[node name="SubViewport" type="SubViewport" parent="CanvasLayer/jinhua"]
transparent_bg = true
size = Vector2i(390, 230)

[node name="RichTextLabel" type="RichTextLabel" parent="CanvasLayer/jinhua/SubViewport"]
material = SubResource("ShaderMaterial_2l3yw")
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -180.0
offset_top = -115.0
offset_right = 333.0
offset_bottom = 195.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.7, 0.7)
mouse_filter = 2
theme_override_constants/outline_size = 20
theme_override_font_sizes/normal_font_size = 256
bbcode_enabled = true
text = "[rainbow freq=1.0 sat=0.8 val=0.8 speed=0.2]进化[/rainbow]"
fit_content = true
scroll_active = false
autowrap_mode = 0

[node name="tanqiu" type="Control" parent="CanvasLayer"]
layout_mode = 3
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -405.0
offset_top = 10.0
offset_right = 95.0
offset_bottom = 310.0
grow_horizontal = 2
scale = Vector2(0.525, 0.525)
pivot_offset = Vector2(250, 150)
size_flags_horizontal = 4
size_flags_vertical = 4

[node name="tan" type="RigidBody2D" parent="CanvasLayer/tanqiu"]
position = Vector2(2.33333, -7)
collision_layer = 2147483648
collision_mask = 2147483648
mass = 2.0
physics_material_override = SubResource("PhysicsMaterial_sg87u")
lock_rotation = true
linear_damp_mode = 1
angular_damp_mode = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="CanvasLayer/tanqiu/tan"]
position = Vector2(109.667, 120)
shape = SubResource("CircleShape2D_i331m")

[node name="Label" type="Label" parent="CanvasLayer/tanqiu/tan"]
clip_contents = true
offset_left = 27.6667
offset_top = 13.0
offset_right = 291.667
offset_bottom = 277.0
scale = Vector2(0.9, 0.9)
size_flags_vertical = 1
theme_override_constants/outline_size = 15
theme_override_font_sizes/font_size = 200
text = "弹"
autowrap_mode = 3

[node name="qiu" type="RigidBody2D" parent="CanvasLayer/tanqiu"]
position = Vector2(239.667, 34.3333)
collision_layer = 2147483648
collision_mask = 2147483648
physics_material_override = SubResource("PhysicsMaterial_2l3yw")
linear_damp_mode = 1
angular_damp_mode = 1

[node name="CollisionShape2D" type="CollisionShape2D" parent="CanvasLayer/tanqiu/qiu"]
position = Vector2(108.333, 95.6667)
shape = SubResource("CircleShape2D_0kdh5")

[node name="Label" type="Label" parent="CanvasLayer/tanqiu/qiu"]
clip_contents = true
offset_left = 4.33302
offset_top = -28.3333
offset_right = 268.333
offset_bottom = 250.667
scale = Vector2(0.9, 0.9)
size_flags_vertical = 1
theme_override_constants/outline_size = 15
theme_override_font_sizes/font_size = 230
text = "球"
autowrap_mode = 3

[node name="StaticBody2D" type="StaticBody2D" parent="CanvasLayer/tanqiu"]
position = Vector2(1, -1)
scale = Vector2(2, 2)
collision_layer = 2147483648
collision_mask = 2147483648
physics_material_override = SubResource("PhysicsMaterial_0kdh5")

[node name="CollisionShape2D" type="CollisionShape2D" parent="CanvasLayer/tanqiu/StaticBody2D"]
shape = SubResource("ConcavePolygonShape2D_0kdh5")

[node name="VBoxContainer" type="VBoxContainer" parent="CanvasLayer"]
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -75.0
offset_top = -100.0
offset_right = 75.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(3, 3)
pivot_offset = Vector2(75, 100)
size_flags_horizontal = 0
size_flags_vertical = 8
size_flags_stretch_ratio = 0.0
theme_override_constants/separation = 25

[node name="Setting" type="Button" parent="CanvasLayer/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = " "

[node name="Label" type="Label" parent="CanvasLayer/VBoxContainer/Setting"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -39.9
offset_top = -13.3
offset_right = 358.1
offset_bottom = 108.7
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(0.2, 0.2)
theme_override_font_sizes/font_size = 100
text = "开始游戏"
