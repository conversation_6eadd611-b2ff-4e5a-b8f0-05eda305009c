class_name DeadLine
extends Node2D

# 距离屏幕底部的固定像素距离（基于设计分辨率1280-1093=187）
const BOTTOM_MARGIN: int = 187
# 设计分辨率高度，用于计算相对位置
const DESIGN_HEIGHT: int = 1280
# 子节点引用
@onready var collision_shape: CollisionShape2D = $CollisionShape2D
@onready var line: Sprite2D = $Line

# 存储原始相对位置（相对于主CollisionShape2D的偏移）
var collision_offset: float
var line_offset: float


func _ready():
	# 连接Area2D的body_entered信号
	self.connect("body_entered", Callable(self, "_on_body_entered"))

	# 存储原始的相对位置偏移
	store_original_offsets()

	# 连接viewport尺寸变化信号
	get_viewport().size_changed.connect(update_position)

	# 初始化位置
	update_position()


func store_original_offsets():
	"""存储各个子节点相对于主CollisionShape2D的原始偏移"""
	var main_y: float = 1093.0  # 原始主CollisionShape2D的Y坐标
	collision_offset = 1260.0 - main_y  # 167
	line_offset = 1090.0 - main_y   # -3


func update_position():
	"""根据当前屏幕尺寸更新deadline位置"""
	# 获取当前实际屏幕高度
	var current_screen_height = get_viewport_rect().size.y

	# 计算新的主要Y坐标（距离底部固定距离）
	var new_main_y = current_screen_height - BOTTOM_MARGIN

	# 更新主CollisionShape2D位置
	collision_shape.position.y = new_main_y

	# 更新其他节点位置，保持相对偏移
	line.position.y = new_main_y + line_offset


func _on_body_entered(body) -> void:
	# 检查是否是敌人
	if body is EnemyBase:
		if body.has_method("attack"):
			body.attack()
	# 检查是否是弹球
	if body is BallBase:
		var dir = body.linear_velocity.normalized()
		# 如果球是向上运动的，忽略碰撞
		if dir.y < 0:
			return
		if body.has_method("on_reach_bottom"):
			body.on_reach_bottom()
